import { RequestMethod } from '@nestjs/common';

export const enquiryAuthorizedRoutes = [
  {
    path: '/enquiry/create',
    method: RequestMethod.POST,
    permissions: '*',
    authenticate: true,
    authorize: true,
  },
  {
    path: '/enquiry/transfer',
    method: RequestMethod.PATCH,
    permissions: '*',
    authenticate: true,
    authorize: false,
  },
  {
    path: '/enquiry/reassign',
    method: RequestMethod.PATCH,
    permissions: '*',
    authenticate: true,
    authorize: false,
  },
  {
    path: '/enquiry/reassign',
    method: RequestMethod.POST,
    permissions: '*',
    authenticate: true,
    authorize: false,
  },
  {
    path: '/enquiry/eligible-grade',
    method: RequestMethod.GET,
    permissions: '*',
    authenticate: false,
    authorize: false,
  },
  {
    path: '/enquiry/timeline/event-sub-types',
    method: RequestMethod.GET,
    permissions: '*',
    authenticate: true,
    authorize: false,
  },
  {
    path: '/enquiry/merge/:targetEnquiryId',
    method: RequestMethod.PATCH,
    permissions: '*',
    authenticate: true,
    authorize: false,
  },
  {
    path: '/enquiry/finance/enquiry-details',
    method: RequestMethod.GET,
    permissions: '*',
    authenticate: false,
    authorize: false,
  },
  {
    path: '/enquiry/finance/enquiry-list/search',
    method: RequestMethod.GET,
    permissions: '*',
    authenticate: false,
    authorize: false,
  },
  {
    path: '/enquiry/finance/payment-status',
    method: RequestMethod.PATCH,
    permissions: '*',
    authenticate: false,
    authorize: false,
  },
  {
    path: '/enquiry/:enquiryId/move-to-next-stage',
    method: RequestMethod.PATCH,
    permissions: '*',
    authenticate: true,
    authorize: false,
  },
  {
    path: '/enquiry/:enquiryId',
    method: RequestMethod.PATCH,
    permissions: '*',
    authenticate: true,
    authorize: true,
  },
  {
    path: '/enquiry/:enquiryId',
    method: RequestMethod.GET,
    permissions: '*',
    authenticate: true,
    authorize: false,
  },
  {
    path: '/enquiry/cc/list',
    method: RequestMethod.POST,
    permissions: '*',
    authenticate: true,
    authorize: true,
  },
  {
    path: '/enquiry/:enquiryId/parent-details',
    method: RequestMethod.PATCH,
    permissions: '*',
    authenticate: true,
    authorize: false,
  },
  {
    path: '/enquiry/:enquiryId/contact-details',
    method: RequestMethod.PATCH,
    permissions: '*',
    authenticate: true,
    authorize: false,
  },
  {
    path: '/enquiry/:enquiryId/medical-details',
    method: RequestMethod.PATCH,
    permissions: '*',
    authenticate: true,
    authorize: false,
  },
  {
    path: '/enquiry/:enquiryId/bank-details',
    method: RequestMethod.PATCH,
    permissions: '*',
    authenticate: true,
    authorize: false,
  },
  {
    path: '/enquiry/:enquiryId/upload-document/:documentId',
    method: RequestMethod.POST,
    permissions: '*',
    authenticate: true,
    authorize: true,
  },
  {
    path: '/enquiry/:enquiryId/document/:documentId',
    method: RequestMethod.GET,
    permissions: '*',
    authenticate: true,
    authorize: false,
  },
  {
    path: '/enquiry/:enquiryId/document/:documentId',
    method: RequestMethod.PATCH,
    permissions: '*',
    authenticate: true,
    authorize: false,
  },
  {
    path: '/enquiry/:enquiryId/timeline',
    method: RequestMethod.GET,
    permissions: '*',
    authenticate: true,
    authorize: false,
  },
  {
    path: '/enquiry/:enquiryId/status',
    method: RequestMethod.PATCH,
    permissions: '*',
    authenticate: true,
    authorize: false,
  },
  {
    path: '/enquiry/:enquiryId/similar-enquiries',
    method: RequestMethod.GET,
    permissions: '*',
    authenticate: true,
    authorize: false,
  },
  {
    path: '/enquiry/:enquiryId/merged-enquiries',
    method: RequestMethod.GET,
    permissions: '*',
    authenticate: true,
    authorize: false,
  },
  {
    path: '/enquiry/:enquiryId/enquirer-details',
    method: RequestMethod.GET,
    permissions: '*',
    authenticate: true,
    authorize: false,
  },
  {
    path: '/enquiry/:enquiryId/transfer-enquiry-details',
    method: RequestMethod.GET,
    permissions: '*',
    authenticate: true,
    authorize: false,
  },
  {
    path: '/enquiry/:enquiryId/reassign-enquiry-details',
    method: RequestMethod.GET,
    permissions: '*',
    authenticate: true,
    authorize: false,
  },
  {
    path: '/enquiry/list/global-search',
    method: RequestMethod.GET,
    permissions: '*',
    authenticate: true,
    authorize: true,
  },
  {
    path: '/enquiry/:enquiryId/merge-enquiry-details',
    method: RequestMethod.POST,
    permissions: '*',
    authenticate: true,
    authorize: false,
  },
  {
    path: '/enquiry/:enquiryId/merge-enquiry-details',
    method: RequestMethod.GET,
    permissions: '*',
    authenticate: true,
    authorize: false,
  },
  {
    path: '/enquiry/:enquiryId/:schoolId/generate-terms-and-conditions-pdf',
    method: RequestMethod.GET,
    permissions: '*',
    authenticate: true,
    authorize: false,
  },
  {
    path: '/enquiry/admission-status-update',
    method: RequestMethod.POST,
    permissions: '*',
    authenticate: false,
    authorize: false,
  },
  {
    path: '/enquiry/edit-fee-attached',
    method: RequestMethod.POST,
    permissions: '*',
    authenticate: false,
    authorize: false,
  },
  {
    path: '/enquiry/admission-approvel/:enquiryNumber',
    method: RequestMethod.GET,
    permissions: '*',
    authenticate: false,
    authorize: false,
  },
  {
    path: '/enquiry/:academicYearId/enquiry-report',
    method: RequestMethod.GET,
    permissions: '*',
    authenticate: true,
    authorize: false,
  },
  {
    path: '/enquiry/:academicYearId/admission-enquiry-report',
    method: RequestMethod.GET,
    permissions: '*',
    authenticate: true,
    authorize: false,
  },
];
