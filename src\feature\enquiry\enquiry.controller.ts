import {
  Body,
  Controller,
  Get,
  HttpStatus,
  Inject,
  Param,
  ParseBoolPipe,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  Req,
  Res,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiTags,
} from '@nestjs/swagger';
import { RedisService } from 'ampersand-common-module';
import { Request, Response } from 'express';

import {
  extractCreatedByDetailsFromBody,
  LoggerService,
  ResponseService,
} from '../../utils';
import { RequestValidationError } from '../../utils';
import { RegexValidationPipe } from '../../validation';
import { EEnquiryEventType } from '../enquiryLog/enquiryLog.type';
import { UpdateIvtEnquiryStatusDto } from './dto';
import {
  AddKitNumberRequestDto,
  ChangeEnquiryStatusResponseDto,
  CreateEnquiryResponseDto,
  FilterDto,
  FinanceEnquiryDetailsSearchRequestDto,
  GetEnquiryTimelineResponseDto,
  GetFileRequestDto,
  GetFinanceEnquiryDetailsResponseDto,
  GetFinanceEnquiryListResponseDto,
  GetMergeEnquiryDetailsApiResponse,
  GetUploadedDocumentUrlResponseDto,
  MoveToNextStageRequestDto,
  ReassignEnquiryRequestDto,
  ReassignRequestDto,
  TransferEnquiryRequestDto,
  UpdateBankDetailsDto,
  UpdateBankDetailsResponseDto,
  UpdateContactDetailsRequestDto,
  UpdateContactDetailsResponseDto,
  UpdateEnquiryParentDetailsResponseDto,
  UpdateEnquiryStatusRequestDto,
  UpdateMedicalDetailsDto,
  UpdateMedicalDetailsResponseDto,
  UpdateParentDetailsRequestDto,
  UpdatePaymentStatusRequestBodyDto,
} from './dto/apiResponse.dto';
import { GetMergeDto, PostMergeDto } from './dto/mergeEnquiry.dto';
import { CheckFeePayload, UpdateAcStudentGuardianArrayDto, UpDateAcStudentGuardianDto, UpdateAdmissionDto, validEmailMobileCoGloble } from './dto/updateAdmission.dto';
import { EnquiryService } from './enquiry.service';
import { EEnquiryStatus } from './enquiry.type';
import { EnquiryStageUpdateService } from './EnquiryStageUpdate.service';

@ApiTags('Enquiry')
@ApiBearerAuth('JWT-auth')
@Controller('enquiry')
export class EnquiryController {
  constructor(
    private loggerService: LoggerService,
    private responseService: ResponseService,
    private enquiryService: EnquiryService,
    private enquiryStageUpdateService: EnquiryStageUpdateService,
    @Inject('REDIS_INSTANCE') private redisInstance: RedisService,
  ) {}

  @Post('upload-file')
  @ApiOperation({ summary: 'Upload document' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Document file to upload',
    type: 'multipart/form-data',
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('file'))
  async uploadFile(
    @Res() res: Response,
    @UploadedFile() file: Express.Multer.File,
  ) {
    try {
      this.loggerService.log(`Upload file Api called`);
      const result = await this.enquiryService.uploadFile(file);
      return this.responseService.sendResponse(
        res,
        HttpStatus.OK,
        result,
        'File uploaded successfully',
      );
    } catch (err: Error | unknown) {
      throw err;
    }
  }

  @Post('file')
  async getUploadedFile(
    @Body() reqBody: GetFileRequestDto,
    @Res() res: Response,
  ) {
    try {
      this.loggerService.log(
        `API to get the file url called with request body : ${reqBody}`,
      );
      const result = await this.enquiryService.getUploadedFileUrl(reqBody);
      return this.responseService.sendResponse(
        res,
        HttpStatus.OK,
        result,
        'Document file URL',
      );
    } catch (err: Error | unknown) {
      throw err;
    }
  }
  @ApiOkResponse({
    status: HttpStatus.OK,
    description: 'Success response',
    type: CreateEnquiryResponseDto,
  })
  @ApiBadRequestResponse({
    status: HttpStatus.OK,
    description: 'Invalid data validation error response',
    type: RequestValidationError,
  })
  @Post('create')
  // @UseInterceptors(ValidateCreateEnquiryRequest)
  async createEnquiry(@Req() req: Request, @Res() res: Response) {
    try {
      this.loggerService.log(
        `Create enquiry API called with request body: ${JSON.stringify(req.body)}`,
      );
      const enquiry = await this.enquiryService.create(req);
      return this.responseService.sendResponse(
        res,
        HttpStatus.CREATED,
        enquiry,
        'Enquiry created',
      );
    } catch (err: Error | unknown) {
      throw err;
    }
  }

  @Get('admission-approvel/:enquiryNumber')
  async approveAdmission(
    @Res() res: Response,
    @Param('enquiryNumber')
    enquiryNumber: string,
  ) {
    try {
      await this.enquiryService.approveAdmissionworkflow(enquiryNumber);
      return this.responseService.sendResponse(
        res,
        HttpStatus.OK,
        enquiryNumber,
        'admission approved',
      );
    } catch (error) {
      return this.responseService.errorResponse(
        res,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
        error.message || 'An error occurred admission approvel',
      );
    }
  }

  @ApiOkResponse({
    status: HttpStatus.OK,
    description: 'Success response',
    type: CreateEnquiryResponseDto,
  })
  @ApiBadRequestResponse({
    status: HttpStatus.OK,
    description: 'Invalid data validation error response',
    type: RequestValidationError,
  })
  @Post('create/website')
  async createWebEnquiry(@Req() req: Request, @Res() res: Response) {
    try {
      this.loggerService.log(
        `Create enquiry from website API called with request body: ${JSON.stringify(req.body)}`,
      );
      req.body.created_by = {
        user_id: -1,
        user_name: 'External User',
        email: '<EMAIL>',
      };
      const enquiry = await this.enquiryService.create(req);
      return this.responseService.sendResponse(
        res,
        HttpStatus.CREATED,
        enquiry,
        'Enquiry created',
      );
    } catch (err: Error | unknown) {
      throw err;
    }
  }

  @Get('enquiryReopen/:enquiryId')
  async enquiryReOpen(
    @Res() res: Response,
    @Param('enquiryId')
    enquiryId: string,
  ) {
    try {
      await this.enquiryService.reOpenEnquiry(enquiryId);
      return this.responseService.sendResponse(
        res,
        HttpStatus.OK,
        { response: 'Done' },
        'ReOpen Enquiry succesfull',
      );
    } catch (error) {
      return this.responseService.errorResponse(
        res,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
        error.message || 'An error occurred',
        error.cause,
      );
    }
  }

  @ApiOkResponse({
    status: HttpStatus.OK,
    description: 'Success response',
    type: UpdateContactDetailsResponseDto,
  })
  @ApiBadRequestResponse({
    status: HttpStatus.OK,
    description: 'Invalid data validation error response',
    type: RequestValidationError,
  })
  @Patch('/transfer')
  async updateTransferEnquiry(
    @Res() res: Response,
    @Body() reqBody: TransferEnquiryRequestDto,
    @Req() req: Request,
  ) {
    try {
      this.loggerService.log(
        `Transfer enquiry API called with the payload : ${JSON.stringify(reqBody)}`,
      );
      const { enquiryIds, school_location } = reqBody;
      const updatedEnquiryDetails = await this.enquiryService.transfer(
        enquiryIds,
        school_location,
        req,
      );
      return this.responseService.sendResponse(
        res,
        HttpStatus.OK,
        updatedEnquiryDetails,
        enquiryIds.length === 1 ? 'Enquiry transfered' : 'Enquiries transfered',
      );
    } catch (err: Error | unknown) {
      throw err;
    }
  }

  @ApiOkResponse({
    status: HttpStatus.OK,
    description: 'Success response',
    type: UpdateContactDetailsResponseDto,
  })
  @ApiBadRequestResponse({
    status: HttpStatus.OK,
    description: 'Invalid data validation error response',
    type: RequestValidationError,
  })
  @Patch('reassign')
  async updateReassignEnquiry(
    @Res() res: Response,
    @Body() reqBody: ReassignEnquiryRequestDto,
    @Req() req: Request,
  ) {
    try {
      this.loggerService.log(
        `Reassign enquiry API called with the payload : ${JSON.stringify(reqBody)}`,
      );
      const { enquiryIds, ...reassignDetails } = reqBody;
      const updatedEnquiryDetails = await this.enquiryService.reassign(
        enquiryIds,
        reassignDetails,
        req.ip,
      );
      return this.responseService.sendResponse(
        res,
        HttpStatus.OK,
        updatedEnquiryDetails,
        enquiryIds.length === 1 ? 'Enquiry reassigned' : 'Enquiries reassigned',
      );
    } catch (err: Error | unknown) {
      throw err;
    }
  }

  @Post('reassign')
  async reassignEmployee(
    @Res() res: Response,
    @Body() reqBody: ReassignRequestDto,
    @Req() req: Request,
  ) {
    try {
      this.loggerService.log(
        `Reassign enquiry API called with the payload : ${JSON.stringify(reqBody)}`,
      );
      const { school_code, hris_code } = reqBody;
      const updatedEnquiryDetails = await this.enquiryService.reassignEmployee(
        school_code,
        hris_code,
      );
      return this.responseService.sendResponse(
        res,
        HttpStatus.OK,
        updatedEnquiryDetails,
        'Reassigned',
      );
    } catch (err: Error | unknown) {
      throw err;
    }
  }

  @Get('eligible-grade')
  async calculateEligibleGrade(
    @Query('academicYearId', ParseIntPipe) academicYearId: number,
    @Query('schoolId', ParseIntPipe) schoolId: number,
    @Query('dob') dob: string,
    @Res() res: Response,
  ) {
    try {
      this.loggerService.log(
        `Calculate and get eligible grade API called with query string params : academicYearId: ${academicYearId}, schoolId: ${schoolId}, dob: ${dob}`,
      );
      const response = await this.enquiryService.calculateEligibleGrade(
        academicYearId,
        schoolId,
        dob,
      );
      return this.responseService.sendResponse(
        res,
        HttpStatus.OK,
        response,
        'Eligible grade calculated',
      );
    } catch (err: Error | unknown) {
      throw err;
    }
  }

  @ApiOkResponse({
    status: HttpStatus.OK,
    description: 'Success response',
  })
  @ApiBadRequestResponse({
    status: HttpStatus.OK,
    description: 'Invalid data validation error response',
    type: RequestValidationError,
  })
  @ApiQuery({ name: 'eventType', required: true, enum: EEnquiryEventType })
  @Get('timeline/event-sub-types')
  async getEnquiryTimelineEventSubTypes(
    @Res() res: Response,
    @Query('eventType') eventType: EEnquiryEventType,
  ) {
    try {
      this.loggerService.log(
        `API to get the enquiry timeline event sub types called for enquiryType - ${eventType}`,
      );
      const timelineEventSubTypes =
        await this.enquiryService.getTimeLineEventSubTypes(eventType);
      return this.responseService.sendResponse(
        res,
        HttpStatus.OK,
        timelineEventSubTypes,
        'Enquiry timeline event sub types',
      );
    } catch (err: Error | unknown) {
      throw err;
    }
  }

  // @ApiOkResponse({
  //   status: HttpStatus.OK,
  //   description: 'Success response',
  // })
  // @ApiBadRequestResponse({
  //   status: HttpStatus.OK,
  //   description: 'Invalid data validation error response',
  //   type: RequestValidationError,
  // })
  // @ApiParam({ name: 'sourceEnquiryId', required: true, type: String })
  // @ApiParam({ name: 'targetEnquiryId', required: true, type: String })
  // @Patch('merge/:sourceEnquiryId/:targetEnquiryId')
  // async mergeEnquiries(
  //   @Res() res: Response,
  //   @Param('sourceEnquiryId', new RegexValidationPipe(/^[0-9a-fA-F]{24}$/))
  //   sourceEnquiryId: string,
  //   @Param('targetEnquiryId', new RegexValidationPipe(/^[0-9a-fA-F]{24}$/))
  //   targetEnquiryId: string,
  // ) {
  //   try {
  //     this.loggerService.log(
  //       `Merge enquiries API called with sourceEnquiryId: ${sourceEnquiryId} and targetEnquiryId: ${targetEnquiryId}`,
  //     );
  //     await this.enquiryService.mergeEnquiry(sourceEnquiryId, targetEnquiryId);
  //     return this.responseService.sendResponse(
  //       res,
  //       HttpStatus.OK,
  //       {},
  //       'Enquiries merged',
  //     );
  //   } catch (err: Error | unknown) {
  //     throw err;
  //   }
  // }

  @ApiOkResponse({
    status: HttpStatus.OK,
    description: 'Success response',
  })
  @ApiBadRequestResponse({
    status: HttpStatus.OK,
    description: 'Invalid data validation error response',
    type: RequestValidationError,
  })
  @ApiParam({ name: 'targetEnquiryId', required: true, type: String })
  @Patch('merge/:targetEnquiryId')
  async mergeEnquiries(
    @Res() res: Response,
    @Body() body: PostMergeDto,
    @Param('targetEnquiryId', new RegexValidationPipe(/^[0-9a-fA-F]{24}$/))
    targetEnquiryId: string,
  ) {
    try {
      this.loggerService.log(
        `Merge enquiries API called with sourceEnquiryId: ${JSON.stringify(body.enquiryIds)} and targetEnquiryId: ${targetEnquiryId}`,
      );
      await this.enquiryService.mergeEnquiry(targetEnquiryId, body);
      return this.responseService.sendResponse(
        res,
        HttpStatus.OK,
        {},
        'Enquiries merged',
      );
    } catch (err: Error | unknown) {
      throw err;
    }
  }

  @ApiOkResponse({
    status: HttpStatus.OK,
    description: 'Success response',
    type: GetFinanceEnquiryDetailsResponseDto,
  })
  @ApiBadRequestResponse({
    status: HttpStatus.OK,
    description: 'Invalid data validation error response',
    type: RequestValidationError,
  })
  @ApiQuery({ name: 'enquiryId', required: true, type: String })
  @Get('/finance/enquiry-details')
  async getEnquiryDetailsForFinance(
    @Req() req: Request,
    @Res() res: Response,
    @Query('enquiryId', new RegexValidationPipe(/^[0-9a-fA-F]{24}$/))
    enquiryId: string,
  ) {
    try {
      this.loggerService.log(
        `Get enquiry details API called from finance with enquiryId: ${enquiryId}`,
      );
      const result = await this.enquiryService.getEnquiryDetailsForFinance(
        req,
        enquiryId,
      );
      return this.responseService.sendResponse(
        res,
        HttpStatus.OK,
        result,
        'Enquiry details found',
      );
    } catch (err: Error | unknown) {
      throw err;
    }
  }

  @ApiOkResponse({
    status: HttpStatus.OK,
    description: 'Success response',
    type: [GetFinanceEnquiryListResponseDto],
  })
  @ApiBadRequestResponse({
    status: HttpStatus.OK,
    description: 'Invalid data validation error response',
    type: RequestValidationError,
  })
  @Post('/finance/enquiry-list/search')
  async getEnquiryListForFinance(
    @Res() res: Response,
    @Body() reqBody: FinanceEnquiryDetailsSearchRequestDto,
  ) {
    try {
      this.loggerService.log(
        `Get enquiry list search API called from finance with search value : ${JSON.stringify(reqBody)}`,
      );
      const result =
        await this.enquiryService.searchEnquiriesForFinance(reqBody);
      return this.responseService.sendResponse(
        res,
        HttpStatus.OK,
        result,
        result?.length ? 'Enquiry found' : 'Enquiry not found',
      );
    } catch (err: Error | unknown) {
      throw err;
    }
  }

  @ApiOkResponse({
    status: HttpStatus.OK,
    description: 'Success response',
  })
  @ApiBadRequestResponse({
    status: HttpStatus.OK,
    description: 'Invalid data validation error response',
    type: RequestValidationError,
  })
  @Post('/finance/payment-status')
  async updatePaymentStatus(
    @Req() req: Request,
    @Res() res: Response,
    @Body() reqBody: UpdatePaymentStatusRequestBodyDto,
  ) {
    try {
      this.loggerService.log(
        `Update payment status API called from finance with payload ${JSON.stringify(reqBody)}`,
      );
      await this.enquiryService.handlePaymentDetails(reqBody, req);
      return this.responseService.sendResponse(
        res,
        HttpStatus.OK,
        {},
        'Payment status updated',
      );
    } catch (err: Error | unknown) {
      throw err;
    }
  }

  @ApiOkResponse({
    status: HttpStatus.OK,
    description: 'Success response',
  })
  @ApiBadRequestResponse({
    status: HttpStatus.OK,
    description: 'Invalid data validation error response',
    type: RequestValidationError,
  })
  @Patch(':enquiryId/move-to-next-stage')
  async moveEnquiryToNextStage(
    @Req() req: Request,
    @Res() res: Response,
    @Param('enquiryId', new RegexValidationPipe(/^[0-9a-fA-F]{24}$/))
    enquiryId: string,
    @Body() reqBody: MoveToNextStageRequestDto,
  ) {
    try {
      this.loggerService.log(
        `Move enquiry to next Stage API called for enquiryId - ${enquiryId} with payload - ${JSON.stringify(reqBody)}`,
      );
      const object = await this.enquiryStageUpdateService.moveToNextStage(
        enquiryId,
        reqBody.currentStage,
        req,
      );
      return this.responseService.sendResponse(
        res,
        HttpStatus.OK,
        {},
        'Enquiry stages updated',
      );
    } catch (err: Error | unknown) {
      throw err;
    }
  }

  @ApiOkResponse({
    status: HttpStatus.OK,
    description: 'Success response',
  })
  @ApiBadRequestResponse({
    status: HttpStatus.OK,
    description: 'Invalid data validation error response',
    type: RequestValidationError,
  })
  // @UseInterceptors(ValidateUpdateEnquiryRequest)
  @Patch(':enquiryId')
  async updateEnquiry(
    @Req() req: Request,
    @Res() res: Response,
    @Param('enquiryId', new RegexValidationPipe(/^[0-9a-fA-F]{24}$/))
    enquiryId: string,
    @Body() reqBody: any,
  ) {
    try {
      this.loggerService.log(
        `Update enquiry registration API called with payload - ${JSON.stringify(reqBody)}`,
      );

      const createdByDetails = extractCreatedByDetailsFromBody(req);
      const userInfo = createdByDetails;
      delete req?.body?.created_by;

      const updatedEnquiryDetails = await this.enquiryService.update(
        enquiryId,
        reqBody,
        userInfo,
        req,
      );
      return this.responseService.sendResponse(
        res,
        HttpStatus.OK,
        updatedEnquiryDetails,
        'Enquiry registration updated',
      );
    } catch (err: Error | unknown) {
      throw err;
    }
  }

  @ApiOkResponse({
    status: HttpStatus.OK,
    description: 'Success response',
  })
  @ApiBadRequestResponse({
    status: HttpStatus.OK,
    description: 'Invalid data validation error response',
    type: RequestValidationError,
  })
  @Get(':enquiryId')
  async getEnquiryDetails(
    @Res() res: Response,
    @Param('enquiryId', new RegexValidationPipe(/^[0-9a-fA-F]{24}$/))
    enquiryId: string,
    @Query('platform') platform: string,
  ) {
    try {
      this.loggerService.log(
        `Get enquiry details by enquiry id API called with id: ${enquiryId}`,
      );
      const enquiryDetails =
        await this.enquiryService.getEnquiryDetails(enquiryId);
      return this.responseService.sendResponse(
        res,
        enquiryDetails ? HttpStatus.OK : HttpStatus.NOT_FOUND,
        enquiryDetails ?? {},
        enquiryDetails ? 'Enquiry details found' : 'Enquiry details not found',
      );
    } catch (err: Error | unknown) {
      throw err;
    }
  }

  @ApiOkResponse({
    status: HttpStatus.OK,
    description: 'Success response',
  })
  @ApiBadRequestResponse({
    status: HttpStatus.OK,
    description: 'Invalid data validation error response',
    type: RequestValidationError,
  })
  @Post('cc/list')
  @ApiQuery({ name: 'page', required: false, type: String })
  @ApiQuery({ name: 'size', required: false, type: String })
  async getEnquiryDetailsCC(
    @Req() req: Request,
    @Res() res: Response,
    @Query('page') page: number,
    @Query('size') size: number,
    @Body() filter: FilterDto,
  ) {
    try {
      this.loggerService.log(`Post cc enquiries list api called`);
      const filtersArray = filter;
      const createdByDetails = extractCreatedByDetailsFromBody(req);
      const { user_id } = createdByDetails;

      const cacheKey = `cc_list:${user_id}:${page}:${size}:${JSON.stringify(filtersArray)}`;

      const cachedData = await this.redisInstance?.getData(cacheKey);
      if (cachedData) {
        this.loggerService.log(`Cache hit for key: ${cacheKey}`);
        return this.responseService.sendResponse(
          res,
          HttpStatus.OK,
          {
            ...cachedData,
            _fromCache: true,
            _cachedAt: new Date().toISOString(),
          },
          'Enquiries found (from cache)',
        );
      }

      const enquiryDetails = await this.enquiryService.getEnquiryDetailsCC(
        req,
        page,
        size,
        filtersArray.filters,
      );

      await this.redisInstance?.setData(cacheKey, enquiryDetails, 300);

      return this.responseService.sendResponse(
        res,
        HttpStatus.OK,
        {
          ...enquiryDetails,
          _fromCache: false,
          _cachedAt: null,
        },
        'Enquiries found',
      );
    } catch (err: Error | unknown) {
      throw err;
    }
  }

  @ApiOkResponse({
    status: HttpStatus.OK,
    description: 'Success response',
    type: UpdateEnquiryParentDetailsResponseDto,
  })
  @ApiBadRequestResponse({
    status: HttpStatus.OK,
    description: 'Invalid data validation error response',
    type: RequestValidationError,
  })
  @Patch(':enquiryId/parent-details')
  async addParentDetails(
    @Req() req: Request,
    @Res() res: Response,
    @Param('enquiryId', new RegexValidationPipe(/^[0-9a-fA-F]{24}$/))
    enquiryId: string,
    @Body() reqBody: UpdateParentDetailsRequestDto,
  ) {
    try {
      this.loggerService.log(
        `Update enquiry parent details API called with the payload : ${JSON.stringify(reqBody)}`,
      );
      const updatedEnquiryDetails = await this.enquiryService.update(
        enquiryId,
        reqBody,
        null,
        req,
      );
      return this.responseService.sendResponse(
        res,
        HttpStatus.OK,
        updatedEnquiryDetails,
        'Parent details updated in enquiry',
      );
    } catch (err: Error | unknown) {
      throw err;
    }
  }

  @ApiOkResponse({
    status: HttpStatus.OK,
    description: 'Success response',
    type: UpdateContactDetailsResponseDto,
  })
  @ApiBadRequestResponse({
    status: HttpStatus.OK,
    description: 'Invalid data validation error response',
    type: RequestValidationError,
  })
  @Patch(':enquiryId/contact-details')
  async addContactDetails(
    @Req() req: Request,
    @Res() res: Response,
    @Param('enquiryId', new RegexValidationPipe(/^[0-9a-fA-F]{24}$/))
    enquiryId: string,
    @Body() reqBody: UpdateContactDetailsRequestDto,
  ) {
    try {
      this.loggerService.log(
        `Update enquiry contact details API called with the payload : ${JSON.stringify(reqBody)}`,
      );
      const updatedEnquiryDetails = await this.enquiryService.update(
        enquiryId,
        reqBody,
        null,
        req,
      );
      return this.responseService.sendResponse(
        res,
        HttpStatus.OK,
        updatedEnquiryDetails,
        'Contact details updated in enquiry',
      );
    } catch (err: Error | unknown) {
      throw err;
    }
  }

  @ApiOkResponse({
    status: HttpStatus.OK,
    description: 'Success response',
    type: UpdateMedicalDetailsResponseDto,
  })
  @ApiBadRequestResponse({
    status: HttpStatus.OK,
    description: 'Invalid data validation error response',
    type: RequestValidationError,
  })
  @Patch(':enquiryId/medical-details')
  async addMedicalDetails(
    @Req() req: Request,
    @Res() res: Response,
    @Param('enquiryId', new RegexValidationPipe(/^[0-9a-fA-F]{24}$/))
    enquiryId: string,
    @Body() reqBody: UpdateMedicalDetailsDto,
  ) {
    try {
      this.loggerService.log(
        `Update medical contact details API called with the payload : ${JSON.stringify(reqBody)}`,
      );
      const updatedEnquiryDetails = await this.enquiryService.update(
        enquiryId,
        reqBody,
        null,
        req,
      );
      return this.responseService.sendResponse(
        res,
        HttpStatus.OK,
        updatedEnquiryDetails,
        'Medical details updated in enquiry',
      );
    } catch (err: Error | unknown) {
      throw err;
    }
  }

  @ApiOkResponse({
    status: HttpStatus.OK,
    description: 'Success response',
    type: UpdateBankDetailsResponseDto,
  })
  @ApiBadRequestResponse({
    status: HttpStatus.OK,
    description: 'Invalid data validation error response',
    type: RequestValidationError,
  })
  @Patch(':enquiryId/bank-details')
  async addBankDetails(
    @Req() req: Request,
    @Res() res: Response,
    @Param('enquiryId', new RegexValidationPipe(/^[0-9a-fA-F]{24}$/))
    enquiryId: string,
    @Body() reqBody: UpdateBankDetailsDto,
  ) {
    try {
      this.loggerService.log(
        `Update bank contact details API called with the payload : ${JSON.stringify(reqBody)}`,
      );
      let enquiryDetails = { ...reqBody.data };
      const { upi } = reqBody.data;
      if (upi) {
        const encrytedUpiID = this.enquiryService.encryptData(upi);
        enquiryDetails = { ...enquiryDetails, upi: encrytedUpiID };
      }
      const updatedEnquiryDetails = await this.enquiryService.update(
        enquiryId,
        reqBody,
        null,
        req,
      );
      return this.responseService.sendResponse(
        res,
        HttpStatus.OK,
        updatedEnquiryDetails,
        'Bank details updated in enquiry',
      );
    } catch (err: Error | unknown) {
      throw err;
    }
  }

  @Post(':enquiryId/upload-document/:documentId')
  @ApiOperation({ summary: 'Upload document' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Document file to upload',
    type: 'multipart/form-data',
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('file'))
  async uploadEnquiryDocument(
    @Req() req: Request,
    @Res() res: Response,
    @Param('enquiryId', new RegexValidationPipe(/^[0-9a-fA-F]{24}$/))
    enquiryId: string,
    @Param('documentId', ParseIntPipe) documentId: number,
    @UploadedFile() file: Express.Multer.File,
  ) {
    try {
      this.loggerService.log(
        `Upload document Api called with request param : enquiryId: ${enquiryId}`,
      );
      const result = await this.enquiryService.uploadEnquiryDocument(
        req,
        enquiryId,
        documentId,
        file,
      );
      return this.responseService.sendResponse(
        res,
        HttpStatus.OK,
        result,
        'Document uploaded',
      );
    } catch (err: Error | unknown) {
      throw err;
    }
  }

  @ApiOkResponse({
    status: HttpStatus.OK,
    description: 'Success response',
    type: GetUploadedDocumentUrlResponseDto,
  })
  @ApiBadRequestResponse({
    status: HttpStatus.OK,
    description: 'Invalid data validation error response',
    type: RequestValidationError,
  })
  @Get(':enquiryId/document/:documentId')
  async getUploadedDocumentUrl(
    @Res() res: Response,
    @Param('enquiryId', new RegexValidationPipe(/^[0-9a-fA-F]{24}$/))
    enquiryId: string,
    @Param('documentId', ParseIntPipe) documentId: number,
    @Query('download', ParseBoolPipe) download: boolean,
  ) {
    try {
      this.loggerService.log(
        `API to get uploaded document url called with enquiryId - ${enquiryId}, documentId - ${documentId}`,
      );
      const enquiryTimeline = await this.enquiryService.getUploadedDocumentUrl(
        enquiryId,
        documentId,
        download,
      );
      return this.responseService.sendResponse(
        res,
        HttpStatus.OK,
        enquiryTimeline,
        'Document url',
      );
    } catch (err: Error | unknown) {
      throw err;
    }
  }

  @ApiOkResponse({
    status: HttpStatus.OK,
    description: 'Success response',
    type: GetUploadedDocumentUrlResponseDto,
  })
  @ApiBadRequestResponse({
    status: HttpStatus.OK,
    description: 'Invalid data validation error response',
    type: RequestValidationError,
  })
  @Patch(':enquiryId/document/:documentId')
  async deleteUploadedDocumentUrl(
    @Res() res: Response,
    @Param('enquiryId', new RegexValidationPipe(/^[0-9a-fA-F]{24}$/))
    enquiryId: string,
    @Param('documentId', ParseIntPipe) documentId: number,
    @Query('delete') deleted: string,
    @Query('verify') verify: string,
  ) {
    try {
      this.loggerService.log(
        `API to delete or verify uploaded document url called with enquiryId - ${enquiryId}, documentId - ${documentId}, deleted - ${deleted}, verify - ${verify}`,
      );
      if (deleted && deleted === 'true') {
        await this.enquiryService.deleteUploadedDocument(enquiryId, documentId);
        return this.responseService.sendResponse(
          res,
          HttpStatus.OK,
          {},
          'Document deleted',
        );
      }
      if (verify) {
        await this.enquiryService.verifyUploadedDocument(
          enquiryId,
          documentId,
          verify === 'true' ? true : false,
        );
        return this.responseService.sendResponse(
          res,
          HttpStatus.OK,
          {},
          'Document verified',
        );
      }
    } catch (err: Error | unknown) {
      throw err;
    }
  }

  @ApiOkResponse({
    status: HttpStatus.OK,
    description: 'Success response',
    type: GetEnquiryTimelineResponseDto,
  })
  @ApiBadRequestResponse({
    status: HttpStatus.OK,
    description: 'Invalid data validation error response',
    type: RequestValidationError,
  })
  @ApiQuery({ name: 'eventType', required: false, type: String })
  @ApiQuery({ name: 'eventSubType', required: false, type: String })
  @Get(':enquiryId/timeline')
  async getEnquiryTimeline(
    @Res() res: Response,
    @Query('eventType') eventType: string,
    @Query('eventSubType') eventSubType: string,
    @Param('enquiryId', new RegexValidationPipe(/^[0-9a-fA-F]{24}$/))
    enquiryId: string,
  ) {
    try {
      this.loggerService.log(
        `API to get the enquiry timeline called for enquiryId - ${enquiryId}`,
      );
      const filter = {};
      if (eventType) filter['eventType'] = eventType;
      if (eventSubType) filter['eventSubType'] = eventSubType;
      const enquiryTimeline = await this.enquiryService.getEnquiryTimeline(
        enquiryId,
        filter,
      );
      return this.responseService.sendResponse(
        res,
        HttpStatus.OK,
        enquiryTimeline,
        'Enquiry timeline',
      );
    } catch (err: Error | unknown) {
      throw err;
    }
  }

  @ApiOkResponse({
    status: HttpStatus.OK,
    description: 'Success response',
    type: ChangeEnquiryStatusResponseDto,
  })
  @ApiBadRequestResponse({
    status: HttpStatus.OK,
    description: 'Invalid data validation error response',
    type: RequestValidationError,
  })
  @Patch(':enquiryId/status')
  async changeEnquiryStatus(
    @Res() res: Response,
    @Param('enquiryId', new RegexValidationPipe(/^[0-9a-fA-F]{24}$/))
    enquiryId: string,
    @Query('status') status: EEnquiryStatus,
    @Body() reqBody: UpdateEnquiryStatusRequestDto,
  ) {
    try {
      this.loggerService.log(
        `Update enquiry status API called with enquiryId : ${enquiryId} and status : ${status}`,
      );
      await this.enquiryService.updateEnquiryStatus(enquiryId, status, reqBody);
      return this.responseService.sendResponse(
        res,
        HttpStatus.OK,
        {},
        'Enquiry status changed',
      );
    } catch (err: Error | unknown) {
      throw err;
    }
  }
  @ApiOkResponse({
    status: HttpStatus.OK,
    description: 'Success response',
  })
  @ApiBadRequestResponse({
    status: HttpStatus.OK,
    description: 'Invalid data validation error response',
    type: RequestValidationError,
  })
  @ApiParam({ name: 'enquiryId', required: true, type: String })
  @Get('/:enquiryId/similar-enquiries')
  async getSimilarEnquiries(
    @Req() req: Request,
    @Res() res: Response,
    @Param('enquiryId', new RegexValidationPipe(/^[0-9a-fA-F]{24}$/))
    enquiryId: string,
  ) {
    try {
      this.loggerService.log(
        `Get similar enquiries API called with enquiryId: ${enquiryId}`,
      );
      const result = await this.enquiryService.getSimilarEnquiries(
        enquiryId,
        req.query?.user_id ? +req.query?.user_id : null,
      );
      return this.responseService.sendResponse(
        res,
        HttpStatus.OK,
        result,
        'Similar enquiries found',
      );
    } catch (err: Error | unknown) {
      throw err;
    }
  }

  @Get('/:enquiryId/merged-enquiries')
  async getMergedEnquiry(
    @Res() res: Response,
    @Param('enquiryId', new RegexValidationPipe(/^[0-9a-fA-F]{24}$/))
    enquiryId: string,
  ) {
    try {
      this.loggerService.log(
        `Get merged enquiries API called with enquiryId: ${enquiryId}`,
      );
      const result = await this.enquiryService.getMergedEnquiries(enquiryId);
      return this.responseService.sendResponse(
        res,
        HttpStatus.OK,
        result,
        'Merged enquiries',
      );
    } catch (err: Error | unknown) {
      throw err;
    }
  }

  @ApiOkResponse({
    status: HttpStatus.OK,
    description: 'Success response',
  })
  @ApiBadRequestResponse({
    status: HttpStatus.OK,
    description: 'Invalid data validation error response',
    type: RequestValidationError,
  })
  @ApiQuery({ name: 'pageSize', type: Number, required: true })
  @ApiQuery({ name: 'pageNumber', type: Number, required: true })
  @Get(':enquiryId/enquirer-details')
  async getEnquirerDetails(
    @Res() res: Response,
    @Param('enquiryId', new RegexValidationPipe(/^[0-9a-fA-F]{24}$/))
    enquiryId: string,
    @Query('pageSize', ParseIntPipe) pageSize: number,
    @Query('pageNumber', ParseIntPipe) pageNumber: number,
  ) {
    try {
      this.loggerService.log(
        `API to get the enquirer details is called with enquiryId: ${enquiryId}`,
      );
      const enquirerDetails = await this.enquiryService.getEnquirerDetails(
        enquiryId,
        pageSize,
        pageNumber,
      );
      return this.responseService.sendResponse(
        res,
        HttpStatus.OK,
        enquirerDetails,
        'Enquirer details found',
      );
    } catch (err: Error | unknown) {
      throw err;
    }
  }

  @ApiOkResponse({
    status: HttpStatus.OK,
    description: 'Success response',
  })
  @ApiBadRequestResponse({
    status: HttpStatus.OK,
    description: 'Invalid data validation error response',
    type: RequestValidationError,
  })
  @ApiParam({ name: 'enquiryId', required: true, type: String })
  @Get('/:enquiryId/transfer-enquiry-details')
  async getTransferEnquiryDetails(
    @Res() res: Response,
    @Param('enquiryId', new RegexValidationPipe(/^[0-9a-fA-F]{24}$/))
    enquiryId: string,
  ) {
    try {
      this.loggerService.log(
        `Get transfer enquiry details API called with enquiryId: ${enquiryId}`,
      );
      const result =
        await this.enquiryService.getEnquiryTransferDetails(enquiryId);
      return this.responseService.sendResponse(
        res,
        HttpStatus.OK,
        result,
        'Transfer enquiry details found',
      );
    } catch (err: Error | unknown) {
      throw err;
    }
  }

  @ApiOkResponse({
    status: HttpStatus.OK,
    description: 'Success response',
  })
  @ApiBadRequestResponse({
    status: HttpStatus.OK,
    description: 'Invalid data validation error response',
    type: RequestValidationError,
  })
  @ApiParam({ name: 'enquiryId', required: true, type: String })
  @Get('/:enquiryId/reassign-enquiry-details')
  async getReassignEnquiryDetails(
    @Res() res: Response,
    @Param('enquiryId', new RegexValidationPipe(/^[0-9a-fA-F]{24}$/))
    enquiryId: string,
  ) {
    try {
      this.loggerService.log(
        `Get reassign enquiry details API called with enquiryId: ${enquiryId}`,
      );
      const result =
        await this.enquiryService.getEnquiryReassignDetails(enquiryId);
      return this.responseService.sendResponse(
        res,
        HttpStatus.OK,
        result,
        'Reassign enquiry details found',
      );
    } catch (err: Error | unknown) {
      throw err;
    }
  }

  @ApiOkResponse({
    status: HttpStatus.OK,
    description: 'Success response',
  })
  @ApiBadRequestResponse({
    status: HttpStatus.OK,
    description: 'Invalid data validation error response',
    type: RequestValidationError,
  })
  @Get('list/global-search')
  @ApiQuery({ name: 'page', required: false, type: String })
  @ApiQuery({ name: 'size', required: false, type: String })
  async searchEnquiryListGloballyByText(
    @Req() req: Request,
    @Res() res: Response,
    @Query('page') page: number,
    @Query('size') size: number,
    @Query('search') globalSearchText: string,
  ) {
    try {
      this.loggerService.log(`Global search enquiry list api called`);
      const enquiryDetails =
        await this.enquiryService.globalSearchEnquiryListing(
          req,
          page,
          size,
          globalSearchText,
        );
      return this.responseService.sendResponse(
        res,
        HttpStatus.OK,
        enquiryDetails,
        'Enquiries found',
      );
    } catch (err: Error | unknown) {
      throw err;
    }
  }

  @ApiParam({ name: 'enquiryId', required: true, type: String })
  @Post('/:enquiryId/merge-enquiry-details')
  async postMergeEnquiries(
    @Body() body: GetMergeDto,
    @Res() res: Response,
    @Param('enquiryId', new RegexValidationPipe(/^[0-9a-fA-F]{24}$/))
    enquiryId: string,
  ) {
    try {
      this.loggerService.log(
        `Get merge enquiry details API called with enquiryId: ${enquiryId}`,
      );
      const result = await this.enquiryService.getMergeEnquiryDetails(
        enquiryId,
        body,
      );
      return this.responseService.sendResponse(
        res,
        HttpStatus.OK,
        result,
        'Merge enquiry details found',
      );
    } catch (err: Error | unknown) {
      throw err;
    }
  }

  @ApiOkResponse({
    status: HttpStatus.OK,
    description: 'Success response',
    type: GetMergeEnquiryDetailsApiResponse,
  })
  @ApiBadRequestResponse({
    status: HttpStatus.OK,
    description: 'Invalid data validation error response',
    type: RequestValidationError,
  })
  @ApiParam({ name: 'enquiryId', required: true, type: String })
  @Get('/:enquiryId/merge-enquiry-details')
  async getMergeEnquiries(
    @Res() res: Response,
    @Param('enquiryId', new RegexValidationPipe(/^[0-9a-fA-F]{24}$/))
    enquiryId: string,
  ) {
    try {
      this.loggerService.log(
        `Get merge enquiry details API called with enquiryId: ${enquiryId}`,
      );
      const result =
        await this.enquiryService.getMergeEnquiryDetails(enquiryId);
      return this.responseService.sendResponse(
        res,
        HttpStatus.OK,
        result,
        'Merge enquiry details found',
      );
    } catch (err: Error | unknown) {
      throw err;
    }
  }

  @ApiParam({ name: 'schoolId', required: true, type: Number })
  @ApiParam({ name: 'enquiryId', required: true, type: String })
  @ApiParam({ name: 'download', required: false, type: Boolean })
  @Get('/:enquiryId/:schoolId/generate-terms-and-conditions-pdf')
  async generateTermsAndConditionPdf(
    @Res() res: Response,
    @Param('enquiryId', new RegexValidationPipe(/^[0-9a-fA-F]{24}$/))
    enquiryId: string,
    @Param('schoolId', ParseIntPipe)
    schoolId: number,
    @Query('download') download?: boolean,
  ) {
    try {
      const result = await this.enquiryService.generateTermsAndConditionPdf(
        enquiryId,
        schoolId,
        download,
      );
      return this.responseService.sendResponse(
        res,
        HttpStatus.OK,
        result,
        'Terms and conditions pdf Generated',
      );
    } catch (err: Error | unknown) {
      throw err;
    }
  }

  @ApiParam({ name: 'enquiryId', required: true, type: String })
  @Get('/:enquiryId/trigger-terms-and-condition-email')
  async triggerTermsAndConditionEmail(
    @Res() res: Response,
    @Param('enquiryId', new RegexValidationPipe(/^[0-9a-fA-F]{24}$/))
    enquiryId: string,
  ) {
    try {
      this.loggerService.log(
        `API to trigger terms and conditions email called for enquiryId - ${enquiryId}`,
      );
      await this.enquiryService.triggerTermsAndConditionEmail(enquiryId);
      return this.responseService.sendResponse(
        res,
        HttpStatus.OK,
        {},
        'Terms and conditions email sent',
      );
    } catch (err: Error | unknown) {
      throw err;
    }
  }

  @ApiParam({ name: 'enquiryId', required: true, type: String })
  @Get('/:enquiryId/accept-terms-and-condition')
  async showTermsAndCondition(
    @Res() res: Response,
    @Param('enquiryId', new RegexValidationPipe(/^[0-9a-fA-F]{24}$/))
    enquiryId: string,
  ) {
    try {
      const result =
        await this.enquiryService.acceptTermsAndCondition(enquiryId);
      return this.responseService.sendResponse(
        res,
        HttpStatus.OK,
        result,
        'Terms and conditions accepted',
      );
    } catch (err: Error | unknown) {
      throw err;
    }
  }

  @Post('admission-status-update')
  async admissionStatusUpdate(
    @Req() req: Request,
    @Res() res: Response,
    @Body() dto: UpdateAdmissionDto,
  ) {
    try {
      const result = await this.enquiryService.updateAdmissionStatus(dto, req);
      return this.responseService.sendResponse(
        res,
        HttpStatus.OK,
        result,
        'Merge enquiry details found',
      );
    } catch (err: Error | unknown) {
      throw err;
    }
  }

  @Post('edit-fee-attached')
  async checkFeeAttached(
    @Req() req: Request,
    @Res() res: Response,
    @Body() body: CheckFeePayload,
  ) {
    try {
      const result = await this.enquiryService.checkIfFeeAttached(body, req);
      return this.responseService.sendResponse(
        res,
        HttpStatus.OK,
        result,
        'succesfull checkIfFeeAttached',
      );
    } catch (error) {
      return this.responseService.errorResponse(
        res,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
        error.message || 'An error occurred',
        error.cause,
      );
    }
  }
//Save guardian details & validate guardian details, if Mobile or email already exists with Other Parents then make them dummy.
  @Post('save-validate/guardian')
  async saveAndVaildateGuardian(
    @Req() req: Request,
    @Res() res: Response,
    @Body() body: CheckFeePayload,
  ) {
    try {
      const result = await this.enquiryService.checkIfFeeAttached(body, req);
      return this.responseService.sendResponse(
        res,
        HttpStatus.OK,
        result,
        'succesfull checkIfFeeAttached',
      );
    } catch (error) {
      return this.responseService.errorResponse(
        res,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
        error.message || 'An error occurred',
        error.cause,
      );
    }
  }
  //API: Validate email or mobile exists in CO global user
    @Post('email-mobile-exist/co-goloble')
    async checkEmailAndMobileExistInCoGloble(
    @Req() req: Request,
    @Res() res: Response,
    @Body() body: validEmailMobileCoGloble,
  ) {
    try {
      const result = await this.enquiryService.findGurdian(body, req);
      return this.responseService.sendResponse(
        res,
        HttpStatus.OK,
        result,
        'succesfull checkIfFeeAttached',
      );
    } catch (error) {
      return this.responseService.errorResponse(
        res,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
        error.message || 'An error occurred',
        error.cause,
      );
    }
  }
//API: If user exists in CO global users then update data else create new entry
  @Post('update/co-goloble')
  async updateCoGloble(
    @Req() req: Request,
    @Res() res: Response,
    @Body() body: CheckFeePayload,
  ) {
    try {
      const result = await this.enquiryService.checkIfFeeAttached(body, req);
      return this.responseService.sendResponse(
        res,
        HttpStatus.OK,
        result,
        'succesfull checkIfFeeAttached',
      );
    } catch (error) {
      return this.responseService.errorResponse(
        res,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
        error.message || 'An error occurred',
        error.cause,
      );
    }
  }
//API: If new entry created in CO global then create new SSO account if not preset else update the SSO account.
  @Post('update/sso-account')
  async updateSsoAccount(
    @Req() req: Request,
    @Res() res: Response,
    @Body() body: CheckFeePayload,
  ) {
    try {
      const result = await this.enquiryService.checkIfFeeAttached(body, req);
      return this.responseService.sendResponse(
        res,
        HttpStatus.OK,
        result,
        'succesfull checkIfFeeAttached',
      );
    } catch (error) {
      return this.responseService.errorResponse(
        res,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
        error.message || 'An error occurred',
        error.cause,
      );
    }
  }
//API: Update Student guardian in ac student guardian
  @Post('upadte/ac-student-guardian')
  async upadteAcStudentGuardian(
    @Req() req: Request,
    @Res() res: Response,
    @Body() body: UpdateAcStudentGuardianArrayDto,
  ) {
    try {
      const result = await this.enquiryService.updateStudentGuardian(body, req);
      return this.responseService.sendResponse(
        res,
        HttpStatus.OK,
        result,
        'succesfull checkIfFeeAttached',
      );
    } catch (error) {
      return this.responseService.errorResponse(
        res,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
        error.message || 'An error occurred',
        error.cause,
      );
    }
  }

  @ApiOkResponse({
    status: HttpStatus.OK,
    description: 'Success response',
  })
  @ApiBadRequestResponse({
    status: HttpStatus.OK,
    description: 'Invalid data validation error response',
    type: RequestValidationError,
  })
  @ApiParam({ name: 'academicYearId', required: true, type: Number })
  @Get('/:academicYearId/enquiry-report')
  async getReport(
    @Res() res: Response,
    @Param('academicYearId', ParseIntPipe)
    academicYearId: number,
    @Req() req: Request,
  ) {
    const createdByDetails = extractCreatedByDetailsFromBody(req);
    const { user_id } = createdByDetails;

    const cacheKey = `user-${user_id}-enquiry-report-${academicYearId}`;
    const cachedData = await this.redisInstance?.getData(cacheKey);

    if (cachedData) {
      return this.responseService.sendResponse(
        res,
        HttpStatus.OK,
        cachedData,
        'Enquiry details report found',
      );
    }
    const result =
      await this.enquiryService.enquiryDetailsReport(academicYearId);

    await this.redisInstance?.setData(cacheKey, result, 60 * 10);

    try {
      return this.responseService.sendResponse(
        res,
        HttpStatus.OK,
        result,
        'Inquiry details report found',
      );
    } catch (err: Error | unknown) {
      throw err;
    }
  }

  @ApiOkResponse({
    status: HttpStatus.OK,
    description: 'Success response',
  })
  @ApiBadRequestResponse({
    status: HttpStatus.OK,
    description: 'Invalid data validation error response',
    type: RequestValidationError,
  })
  @ApiParam({ name: 'academicYearId', required: true, type: Number })
  @Get('/:academicYearId/admission-enquiry-report')
  async getReportForAdmissionEnquiry(
    @Res() res: Response,
    @Param('academicYearId', ParseIntPipe)
    academicYearId: number,
    @Req() req: Request,
  ) {
    const createdByDetails = extractCreatedByDetailsFromBody(req);
    const { user_id } = createdByDetails;

    const cacheKey = `user-${user_id}-admission-enquiry-report-${academicYearId}`;
    const cachedData = await this.redisInstance?.getData(cacheKey);

    if (cachedData) {
      return this.responseService.sendResponse(
        res,
        HttpStatus.OK,
        cachedData,
        'Enquiry details report found',
      );
    }
    const result =
      await this.enquiryService.admissionEnquiryReport(academicYearId);

    await this.redisInstance?.setData(cacheKey, result, 60 * 10);

    try {
      return this.responseService.sendResponse(
        res,
        HttpStatus.OK,
        result,
        'Inquiry details report found',
      );
    } catch (err: Error | unknown) {
      throw err;
    }
  }

  @ApiOkResponse({
    status: HttpStatus.OK,
    description: 'Success response',
  })
  @ApiBadRequestResponse({
    status: HttpStatus.OK,
    description: 'Invalid data validation error response',
    type: RequestValidationError,
  })
  @ApiParam({ name: 'enquiryId', required: true, type: String })
  @Post(':enquiryId/kit-number/add')
  async addKitNumber(
    @Res() res: Response,
    @Param('enquiryId', new RegexValidationPipe(/^[0-9a-fA-F]{24}$/))
    enquiryId: string,
    @Body() reqBody: AddKitNumberRequestDto,
  ) {
    try {
      const response = await this.enquiryService.addKitNumber(
        enquiryId,
        reqBody.kitNumber,
      );
      return this.responseService.sendResponse(
        res,
        HttpStatus.OK,
        {},
        response ? 'Kit number added' : 'Kit number already exists',
      );
    } catch (err: Error | unknown) {
      throw err;
    }
  }

  @ApiOkResponse({
    status: HttpStatus.OK,
    description: 'Success response',
  })
  @ApiBadRequestResponse({
    status: HttpStatus.OK,
    description: 'Invalid data validation error response',
    type: RequestValidationError,
  })
  @ApiParam({ name: 'enquiryId', required: true, type: String })
  @Patch(':enquiryId/ivt-status')
  async updateIvtEnquiryStatus(
    @Res() res: Response,
    @Param('enquiryId', new RegexValidationPipe(/^[0-9a-fA-F]{24}$/))
    enquiryId: string,
    @Body() reqBody: UpdateIvtEnquiryStatusDto,
  ) {
    try {
      this.loggerService.log(
        `API to update IVT enquiry status called with the payload : [EnquiryId] : ${enquiryId}, [RequestBody] : ${JSON.stringify(reqBody)}`,
      );
      await this.enquiryService.updateIvtEnquiryStatus(enquiryId, reqBody);
      return this.responseService.sendResponse(
        res,
        HttpStatus.OK,
        {},
        'IVT inquiry status updated',
      );
    } catch (err: Error | unknown) {
      throw err;
    }
  }
}
