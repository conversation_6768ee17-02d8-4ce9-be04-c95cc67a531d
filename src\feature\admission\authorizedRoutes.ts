import { RequestMethod } from '@nestjs/common';

export const admissionAuthorizedRoutes = [
  {
    path: '/marketing/admission/update-approval-status',
    method: RequestMethod.POST,
    permissions: '*',
    authenticate: true,
    authorize: false,
  },
  {
    path: '/marketing/admission/:enrolmentNumber/student-details',
    method: RequestMethod.GET,
    permissions: '*',
    authenticate: true,
    authorize: false,
  },
  {
    path: '/marketing/admission/:enquiryId/create',
    method: RequestMethod.POST,
    permissions: '*',
    authenticate: true,
    authorize: true,
  },

  {
    path: '/marketing/admission/:enquiryId/subject-details',
    method: RequestMethod.POST,
    permissions: '*',
    authenticate: true,
    authorize: true,
  },
  {
    path: '/marketing/admission/:enquiryId/payment-request',
    method: RequestMethod.POST,
    permissions: '*',
    authenticate: true,
    authorize: false,
  },
  {
    path: 'enquiryReopen/:enquiryId',
    method: RequestMethod.GET,
    permissions: '*',
    authenticate: false,
    authorize: false,
  },
  {
    path: '/marketing/admission/:enquiryId/vas/remove',
    method: RequestMethod.POST,
    permissions: '*',
    authenticate: true,
    authorize: true,
  },
  {
    path: '/marketing/admission/:enquiryId/vas/add',
    method: RequestMethod.POST,
    permissions: '*',
    authenticate: true,
    authorize: true,
  },
  {
    path: '/marketing/admission/:enquiryId/submit-student-detail',
    method: RequestMethod.POST,
    permissions: '*',
    authenticate: true,
    authorize: false,
  },
  {
    path: '/marketing/admission/:enquiryId',
    method: RequestMethod.PATCH,
    permissions: '*',
    authenticate: true,
    authorize: false,
  },
  {
    path: '/marketing/admission/:enquiryId',
    method: RequestMethod.GET,
    permissions: '*',
    authenticate: true,
    authorize: false,
  },
  // {
  //   path: '/marketing/admission/:enquiryId/add-default-fees',
  //   method: RequestMethod.POST,
  //   permissions: '*',
  //   authenticate: true,
  //   authorize: false,
  // },
];
