import { InjectQueue } from '@nestjs/bullmq';
import {
  BadRequestException,
  forwardRef,
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  AuditLogRepository,
  EStorageType,
  HTTP_METHODS,
  mongodbPaginationQuery,
  RedisService,
  StorageService,
} from 'ampersand-common-module';
import axios, { AxiosRequestHeaders } from 'axios';
import { Queue } from 'bullmq';
import * as crypto from 'crypto';
import { formatToTimeZone } from 'date-fns-timezone';
import { Request } from 'express';
import * as fs from 'fs';
import * as moment from 'moment';
import { Document, PipelineStage, Types } from 'mongoose';

import {
  ADMIN_PANEL_URL,
  EMAIL_TEMPLATE_SLUGS,
} from '../../global/global.constant';
import { EmailService } from '../../global/global.email.service';
import { AxiosService, EHttpCallMethods } from '../../global/service';
import { CreatedByDetailsDto } from '../../middleware/auth/auth.dto';
import {
  ADMIN_API_URLS,
  ALL_LEADS_PERMISSION,
  buildFilter,
  EMPLOYEE_ACTIVITY_STATUS,
  extractCreatedByDetailsFromBody,
  FRONTEND_STANDALONE_PAGES_URL,
  getCcReHrisCodes,
  getSdoRoleCodes,
  LoggerService,
  MdmService,
} from '../../utils';
import { MDM_API_URLS } from '../../utils';
import {
  applyTemplate,
  getSessionData,
  isAppRequest,
} from '../../utils/utility-functions';
import { AdmissionRepository } from '../admission/admission.repository';
import { AdmissionService } from '../admission/admission.service';
import { EAdmissionApprovalStatus } from '../admission/admission.type';
import { CsvService } from '../csv/csv.service';
import { EnquiryLogRepository } from '../enquiryLog/enquiryLog.repository';
import { EnquiryLogService } from '../enquiryLog/enquiryLog.service';
import {
  EEnquiryEvent,
  EEnquiryEventSubType,
  EEnquiryEventType,
} from '../enquiryLog/enquiryLog.type';
import { EnquiryStageRepository } from '../enquiryStage/enquiryStage.repository';
import { EnquiryTypeService } from '../enquiryType/enquiryType.service';
import { FileService } from '../file/file.service';
import { MyTaskService } from '../myTask/myTask.service';
import { ETaskEntityType } from '../myTask/myTask.type';
import { PdfService } from '../pdf/pdf.service';
import { WorkflowService } from '../workflow/workflow.service';
import { MasterFieldDto } from './app/dto';
import { UpdateIvtEnquiryStatusDto } from './dto';
import { FilterItemDto } from './dto/apiResponse.dto';
import {
  EnquiryDetails,
  GetMergeDto,
  PostMergeDto,
} from './dto/mergeEnquiry.dto';
import { CheckFeePayload, UpdateAdmissionDto } from './dto/updateAdmission.dto';
import {
  AdmissionStatus,
  ENQUIRY_PRIORITY,
  ENQUIRY_STAGES,
  ENQUIRY_TYPE,
  ENQUIRY_TYPE_SLUG,
  enquiryGlobalSearchFields,
  GLOBAL_ENQUIRY_GENERATOR_ID,
  STATE_AGE_MAPPING,
} from './enquiry.constant';
import { EnquiryRepository } from './enquiry.repository';
import { EnquiryDocument } from './enquiry.schema';
import {
  EEnquiryAdmissionType,
  EEnquiryStageStatus,
  EEnquiryStatus,
  EEnquiryType,
  EParentType,
  EPaymentType,
  RoundRobinAssignedStatus,
} from './enquiry.type';
import { EnquiryHelper } from './enquiryHelper.service';
import { EnquiryStageUpdateService } from './EnquiryStageUpdate.service';

@Injectable()
export class EnquiryService {
  constructor(
    private enquiryRepository: EnquiryRepository,
    private enquiryHelper: EnquiryHelper,
    private storageService: StorageService,
    private enquiryLogService: EnquiryLogService,
    private enquiryLogRepository: EnquiryLogRepository,
    private enquiryStageRepository: EnquiryStageRepository,
    private configService: ConfigService,
    private auditLogRepository: AuditLogRepository,
    private enquiryTypeService: EnquiryTypeService,
    @Inject(forwardRef(() => AdmissionService))
    private admissionService: AdmissionService,
    private mdmService: MdmService,
    private axiosService: AxiosService,
    private admissionRepository: AdmissionRepository,
    private enquiryStageUpdateService: EnquiryStageUpdateService,
    private workflowService: WorkflowService,
    private myTaskService: MyTaskService,
    private pdfService: PdfService,
    private fileService: FileService,
    private loggerService: LoggerService,
    private csvService: CsvService,
    private emailService: EmailService,
    @Inject('REDIS_INSTANCE') private redisInstance: RedisService,
    @InjectQueue('admissionFees') private admissionFeeQueue: Queue,
  ) {
    // console.log(this.checkIfFeeAttached({},req));
  }
  async checkIfFeeAttached(payload: CheckFeePayload, req: any) {
    try {
      const enquiryDetails = await this.getEnquiryDetail(
        payload.enquiry_number,
      );
      const admissionDetail = await this.getAdmissionDetails(
        enquiryDetails._id,
      );

      const hasDefaultFees =
        admissionDetail.default_fees && admissionDetail.default_fees[0];
      let hasAdmissionFees = false;
      if (hasDefaultFees) {
        hasAdmissionFees = admissionDetail.default_fees.some(
          (fee: any) => fee?.fee_type_slug === 'admission',
        );
      }

      const enquiryNeedsUpdate = this.checkIfEnquiryNeedsUpdate(
        enquiryDetails,
        payload,
      );

      if (!hasDefaultFees) {
        console.log(
          'No default fees found, proceeding with fee attachment logic',
        );
        await this.handleNoDefaultFees(
          enquiryDetails,
          admissionDetail,
          payload,
          req,
          enquiryNeedsUpdate,
        );
      } else {
        console.log(
          'Default fees exist, checking if update is needed',
          enquiryDetails,
          payload,
          req,
          enquiryNeedsUpdate,
        );
        await this.handleExistingDefaultFees(
          enquiryDetails,
          payload,
          req,
          enquiryNeedsUpdate,
          hasAdmissionFees,
        );
      }

      console.log('Fee attachment check completed successfully');
    } catch (error) {
      console.error('Error in checkIfFeeAttached:', {
        enquiryNumber: payload.enquiry_number,
        error: error.message,
        stack: error.stack,
      });
      throw new HttpException(
        'Invalid field value',
        HttpStatus.INTERNAL_SERVER_ERROR,
        {
          cause: error,
        },
      );
    }
  }

  private async getEnquiryDetail(enquiryNumber: string) {
    const enquiryDetails =
      await this.enquiryRepository.getByEnquiryNumber(enquiryNumber);

    if (!enquiryDetails) {
      throw new HttpException(
        'No Enquiry found',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    return enquiryDetails;
  }

  private async getAdmissionDetails(enquiryId: string) {
    const admissionDetail = await this.admissionRepository.getByEnquiryId(
      new Types.ObjectId(enquiryId),
    );

    if (!admissionDetail) {
      throw new HttpException(
        'No admissionDetail found',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    return admissionDetail;
  }

  private checkIfEnquiryNeedsUpdate(
    enquiryDetails: any,
    payload: CheckFeePayload,
  ): boolean {
    return (
      enquiryDetails.school_location.id !== payload.school.id ||
      enquiryDetails.brand.id !== payload.brand.id ||
      enquiryDetails.board.id !== payload.board.id ||
      enquiryDetails.student_details.grade.id !== payload.grade.id ||
      enquiryDetails.course.id !== payload.course.id ||
      enquiryDetails.shift.id !== payload.shift.id ||
      enquiryDetails.stream.id !== payload.stream.id
    );
  }

  private async updateEnquiryDetails(
    enquiryId: string,
    payload: CheckFeePayload,
  ) {
    const objectId = new Types.ObjectId(enquiryId);

    const result = await this.enquiryRepository.updateOne(
      { _id: objectId },
      {
        $set: {
          brand: { id: payload.brand.id, value: payload.brand.value },
          board: { id: payload.board.id, value: payload.board.value },
          course: { id: payload.course.id, value: payload.course.value },
          stream: { id: payload.stream.id, value: payload.stream.value },
          shift: { id: payload.shift.id, value: payload.shift.value },
          'student_details.grade': {
            id: payload.grade.id,
            value: payload.grade.value,
          },
          school_location: {
            id: payload.school.id, // Fixed: should be school.id, not grade.id
            value: payload.school.value,
          },
        },
      },
    );

    return result;
  }

  private async fetchSchoolFeeDetails(payload: CheckFeePayload) {
    const schoolDetails = await this.mdmService.fetchDataFromAPI(
      `${MDM_API_URLS.SCHOOL}/${payload.school.id}`,
    );

    if (!schoolDetails) {
      throw new HttpException(
        'No School found',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    const schoolParentId = schoolDetails.data.attributes.school_parent_id;
    const requestParam = `school_parent_id = ${schoolParentId} AND academic_year_id = ${payload.academicYearId.value.slice(-2)} AND grade_id = ${payload.grade.id} AND board_id = ${payload.board.id} AND course_id = ${payload.course.id} AND shift_id = ${payload.shift.id} AND stream_id = ${payload.stream.id} AND guest_school_id IS NULL AND guest_lob_id IS NULL AND fee_type_id IN (1,17,9)`;

    const schoolFeeDetails = await this.mdmService.postDataToAPI(
      MDM_API_URLS.SCHOOL_FEE,
      {
        operator: requestParam,
      },
    );

    if (!schoolFeeDetails || schoolFeeDetails?.data?.schoolFees?.length === 0) {
      throw new HttpException(
        'Fee not found for given enquiry Data',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    return schoolFeeDetails;
  }

  private async processFeesAndPayment(
    enquiryId: string,
    schoolFeeDetails: any,
    payload: CheckFeePayload,
    req: any,
  ) {
    await this.admissionService.updateAdmissionFeeRequst(enquiryId);

    await this.admissionService.addDefaultFees(
      req,
      enquiryId,
      schoolFeeDetails?.data?.schoolFees,
    );

    // De-enroll existing fees
    await this.performBulkDeEnrollment(enquiryId, payload, req);

    // Send payment request
    const paymentRequest = await this.admissionService.sendPaymentRequest(
      enquiryId,
      req,
    );

    return paymentRequest;
  }
  private async getStudentFeeDetails(enquireId, req, payload) {
    const enquire_id = enquireId.toHexString();
    let idArray: any = [];
    const newUrl = process.env.FINANCE_URL;
    const yeartId = 26;
    const yeartValue = String(payload?.academicYearId?.value) || '2025 - 26';

    const feeData = await axios.post(
      `${newUrl}/fee_collection/fee_details`,
      {
        type: 'pending',
        students: [enquire_id],
        academic_years: [yeartId],
      },
      {
        headers: {
          Authorization: req.headers.authorization,
          'Content-Type': 'application/json',
        },
      },
    );

    if (feeData.status !== 200) {
      console.error(`fee detail with status: ${feeData.status}`);
      throw new HttpException(
        'Something went wrong in fee detail',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    if (!feeData?.data?.data?.fees?.[enquire_id]) {
      return idArray;
    }
    const data = feeData?.data?.data.fees[enquire_id][yeartValue];
    idArray = data.map((item) => item.id.toString());
    if (idArray.length == 0) {
      return idArray;
    }

    return idArray;
  }

  private async performBulkDeEnrollment(enquireId, payload, req) {
    const feeIds = await this.getStudentFeeDetails(enquireId, req, payload);
    const newUrl = process.env.FINANCE_URL;

    if (feeIds.length == 0) {
      return;
    }

    const reasonData = await this.mdmService.fetchDataFromAPI(
      `${MDM_API_URLS.DE_ENROLL_REASONS}?filters%5Bname%5D=Different%20Combination%20Requried'`,
    );
    const reasonId = Number(reasonData?.data[0]?.id);

    const denrollDetail = await axios.post(
      `${newUrl}/student-fees/bulk-deenrolment`,
      {
        student_fees_id: feeIds,
        reason_id: reasonId || 152,
      },
      {
        headers: {
          Authorization: req.headers.authorization,
          'Content-Type': 'application/json',
        },
      },
    );

    if (denrollDetail.status !== 200) {
      console.error(
        `De-enrollment failed with status: ${denrollDetail.status}`,
      );
      throw new HttpException(
        'Something went wrong in De-Enrollment',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    return denrollDetail;
  }

  private async handleNoDefaultFees(
    enquiryDetails: any,
    admissionDetail: any,
    payload: CheckFeePayload,
    req: any,
    enquiryNeedsUpdate: boolean,
  ) {
    const schoolFeeDetails = await this.fetchSchoolFeeDetails(payload);
    await this.processFeesAndPayment(
      enquiryDetails._id,
      schoolFeeDetails,
      payload,
      req,
    );
    if (enquiryNeedsUpdate) {
      await this.updateEnquiryDetails(enquiryDetails._id, payload);
    }
  }

  private async handleExistingDefaultFees(
    enquiryDetails: any,
    payload: any,
    req: any,
    enquiryNeedsUpdate: boolean,
    hasAdmissionFees: boolean,
  ) {
    if (enquiryNeedsUpdate) {
      const schoolFeeDetails = await this.fetchSchoolFeeDetails(payload);
      await this.updateEnquiryDetails(enquiryDetails._id, payload);

      await this.processFeesAndPayment(
        enquiryDetails._id,
        schoolFeeDetails,
        payload,
        req,
      );
    } else {
      if (!hasAdmissionFees) {
        const schoolFeeDetails = await this.fetchSchoolFeeDetails(payload);
        await this.processFeesAndPayment(
          enquiryDetails._id,
          schoolFeeDetails,
          payload,
          req,
        );
      } else {
        console.warn(
          'No condition satisfied - enquiry details match and fees exist',
        );
        throw new HttpException(
          'No Condition Satisfied',
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }
    }
  }

  async approveAdmissionworkflow(enquiryNumber: string) {
    try {
      if (!enquiryNumber) {
        throw new NotFoundException('Enquiry number not found');
      }

      await Promise.all([
        this.enquiryRepository.updateOne(
          { enquiry_number: enquiryNumber },
          {
            $set: {
              'enquiry_stages.5.status': 'Approved',
            },
          },
        ),
      ]);
    } catch (error) {
      console.error('Error in admission approvel', {
        error: error.message,
      });
      throw new HttpException(
        'Invalid field value',
        HttpStatus.INTERNAL_SERVER_ERROR,
        {
          cause: error,
        },
      );
    }
  }

  async updateStudentGuardian(body: any, req: any) {
    
    const studentDetail = await this.mdmService.fetchDataFromAPI(
      `${MDM_API_URLS.STUDENTS}/${body.studentId}`,
    );

    if (!studentDetail?.data) {
      throw new HttpException(
        'Student not found',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    const parentDeatil = await this.mdmService.fetchDataFromAPI(
      `${MDM_API_URLS.GUARDIANS}/${body.guardianId}`,
    );

    if (!parentDeatil?.data) {
      throw new HttpException(
        'Parent not found',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    const existingGuardian = await this.mdmService.fetchDataFromAPI(
        `${MDM_API_URLS.STUDENT_GUARDIAN}?filters%5Bname%5D=Different%20Combination%20Requried`,
    );




  }

  async reOpenEnquiry(enquiryId: string) {
    try {
      if (!enquiryId) {
        throw new NotFoundException('Enquiry Id not found');
      }
      const enquiryData = await this.enquiryRepository.getById(
        new Types.ObjectId(enquiryId),
      );

      if (enquiryData?.status !== 'Closed') {
        throw new NotFoundException('Enquiry is not closed');
      }

      await Promise.all([
        this.enquiryRepository.updateOne(
          { _id: enquiryId },
          { status: 'Open' },
        ),
      ]);
      await this.enquiryLogService.createLog({
        enquiry_id: new Types.ObjectId(enquiryId),
        event_type: EEnquiryEventType.ENQUIRY,
        event_sub_type: EEnquiryEventSubType.ENQUIRY_ACTION,
        event: EEnquiryEvent.ENQUIRY_REOPENED,
        created_by: 'System',
        created_by_id: -1,
      });
    } catch (error) {
      console.error('Error in checkIfFeeAttached:', {
        error: error.message,
      });
      throw new HttpException(
        'Invalid field value',
        HttpStatus.INTERNAL_SERVER_ERROR,
        {
          cause: error,
        },
      );
    }
  }

  async setFileUploadStorage() {
    const bucketName = this.configService.get<string>('BUCKET_NAME');
    const folderName = this.configService.get<string>('FOLDER_NAME');

    this.storageService.setStorage(EStorageType.GCS, {
      projectId: this.configService.get<string>('PROJECT_ID'),
      credentials: {
        type: this.configService.get<string>('TYPE'),
        project_id: this.configService.get<string>('PROJECT_ID'),
        private_key_id: this.configService.get<string>('PRIVATE_KEY_ID'),
        private_key: this.configService
          .get<string>('PRIVATE_KEY')
          .replace(/\\n/g, '\n'),
        client_email: this.configService.get<string>('CLIENT_EMAIL'),
        client_id: this.configService.get<string>('GCS_CLIENT_ID'),
        auth_uri: this.configService.get<string>('AUTH_URI'),
        token_uri: this.configService.get<string>('TOKEN_URI'),
        auth_provider_x509_cert_url: this.configService.get<string>(
          'AUTH_PROVIDER_X509_CERT_URL',
        ),
        client_x509_cert_url: this.configService.get<string>(
          'CLIENT_X509_CERT_URL',
        ),
        universe_domain: this.configService.get<string>('UNIVERSAL_DOMAIN'),
      },
      bucketName: bucketName,
      folderName: folderName,
    });
    return { bucketName };
  }

  async create(req: Request) {
    const payload = req.body;

    const { metadata, data } = payload;
    const createdByDetails = extractCreatedByDetailsFromBody(req);
    if (createdByDetails) {
      data.created_by = payload.created_by;
      delete payload?.created_by;
    }
    let anotherStudentDetail;

    // Converting email in lower case
    if (data['parent_details.father_details.email']) {
      data['parent_details.father_details.email'] =
        data['parent_details.father_details.email'].toLowerCase();
    } else if (data['parent_details.mother_details.email']) {
      data['parent_details.mother_details.email'] =
        data['parent_details.mother_details.email'].toLowerCase();
    } else if (data['parent_details.guardian_details.email']) {
      data['parent_details.guardian_details.email'] =
        data['parent_details.guardian_details.email'].toLowerCase();
    }

    const isAnotherChild = payload?.data?.['is_another_child_enquiry'];
    if (payload?.data?.['is_another_child_enquiry'] === 'yes') {
      anotherStudentDetail = {
        first_name: payload?.data?.['another_student_details.first_name'],
        last_name: payload?.data?.['another_student_details.last_name'],
        dob: payload?.data?.['another_student_details.dob'],
      };
      delete payload?.data['another_student_details.type'];
      delete payload.data['another_student_details.first_name'];
      delete payload.data['another_student_details.last_name'];
      delete payload.data['another_student_details.dob'];
    }
    const { form_id, enquiry_type_id } = metadata;
    const enquiryFormDetails = await this.enquiryHelper.getEnquiryFormDetails(
      form_id,
      req.headers.authorization,
    );
    const { inputs: formFields } = enquiryFormDetails;
    const validationObjects =
      this.enquiryHelper.extractValidationObjects(formFields);
    const errors = this.enquiryHelper.validateFormData(data, validationObjects);
    if (Object.keys(errors).length) {
      throw new HttpException('Invalid field value', HttpStatus.BAD_REQUEST, {
        cause: errors,
      });
    }

    const enquiryTypeDetails =
      await this.enquiryTypeService.getEnquiryTypeDetailsWithStageName(
        enquiry_type_id,
      );

    if (!enquiryTypeDetails) {
      throw new HttpException(
        'Invalid enquiry type sent',
        HttpStatus.BAD_REQUEST,
      );
    }

    const { stages, slug } = enquiryTypeDetails;

    const paths = this.enquiryRepository.getSchemaPaths();

    const defaultEnquiryFields =
      await this.enquiryHelper.getDefaultEnquiryFields(createdByDetails);
    const createPayload = this.enquiryHelper.generateEnquirySchema(data, paths);

    const other_details_ =
      await this.enquiryHelper.checkAndAddNewAdmissionConcessionTags(
        slug,
        createPayload.other_details,
        req.headers.authorization,
      );
    createPayload.other_details = other_details_;
    createPayload.other_details['terms_and_conditions_email_sent'] = false; //Setting the flag as false by default
    createPayload.other_details['are_terms_and_condition_accepted'] = true; //Setting the flag as true bypass the terms and conditions check, in future again set the flag as false
    createPayload.enquiry_number = await this.enquiryHelper.getGlobalId(
      GLOBAL_ENQUIRY_GENERATOR_ID,
    );
    let fatherGlobalId = null;
    let motherGlobalId = null;
    let guardianGlobalId = null;
    let parentSsoUsername = null;
    let parentSsoPassword = null;
    switch (data.parent_type) {
      case EParentType.FATHER:
        if (
          createPayload?.parent_details?.father_details?.mobile &&
          createPayload?.parent_details?.father_details?.email
        ) {
          const response = await this.enquiryHelper.getParentGlobalId(
            createPayload.parent_details.father_details.mobile,
            createPayload.parent_details.father_details.email,
            createPayload.parent_details?.father_details?.first_name,
            createPayload.parent_details?.father_details?.last_name,
          );
          fatherGlobalId = response.global_no;
          parentSsoUsername = response.sso_email;
          parentSsoPassword = response.sso_password;
        }
        createPayload.parent_details.father_details.global_id = fatherGlobalId;
        createPayload.parent_details.father_details.sso_username =
          parentSsoUsername;
        createPayload.parent_details.father_details.sso_password =
          parentSsoPassword;
        break;
      case EParentType.MOTHER:
        if (
          createPayload?.parent_details?.mother_details?.mobile &&
          createPayload?.parent_details?.mother_details?.email
        ) {
          const response = await this.enquiryHelper.getParentGlobalId(
            createPayload.parent_details.mother_details.mobile,
            createPayload.parent_details.mother_details.email,
            createPayload.parent_details?.mother_details?.first_name,
            createPayload.parent_details?.mother_details?.last_name,
          );
          motherGlobalId = response.global_no;
          parentSsoUsername = response.sso_email;
          parentSsoPassword = response.sso_password;
        }
        createPayload.parent_details.mother_details.global_id = motherGlobalId;
        createPayload.parent_details.mother_details.sso_username =
          parentSsoUsername;
        createPayload.parent_details.mother_details.sso_password =
          parentSsoPassword;
        break;
      case EParentType.GUARDIAN:
        if (
          createPayload?.parent_details?.guardian_details?.mobile &&
          createPayload?.parent_details?.guardian_details?.email
        ) {
          const response = await this.enquiryHelper.getParentGlobalId(
            createPayload.parent_details.guardian_details.mobile,
            createPayload.parent_details.guardian_details.email,
            createPayload.parent_details?.guardian_details?.first_name,
            createPayload.parent_details?.guardian_details?.last_name,
          );
          guardianGlobalId = response.global_no;
          parentSsoUsername = response.sso_email;
          parentSsoPassword = response.sso_password;
        }
        createPayload.parent_details.guardian_details.global_id =
          guardianGlobalId;
        createPayload.parent_details.guardian_details.sso_username =
          parentSsoUsername;
        createPayload.parent_details.guardian_details.sso_password =
          parentSsoPassword;
        break;
    }

    if (
      createPayload?.student_details?.first_name &&
      createPayload?.student_details?.last_name &&
      createPayload?.student_details?.dob
    ) {
      const studentGlobalId = await this.enquiryHelper.getStudentGlobalId(
        createPayload.student_details.first_name,
        createPayload.student_details.last_name,
        createPayload.student_details.dob,
      );
      createPayload.student_details.global_id = studentGlobalId;
    }

    createPayload.enquiry_type_id = new Types.ObjectId(
      enquiry_type_id as string,
    );
    createPayload.enquiry_form_id = new Types.ObjectId(form_id as string);

    if (
      enquiry_type_id &&
      createPayload?.student_details?.first_name &&
      ((createPayload?.parent_details?.father_details?.mobile &&
        createPayload?.parent_details?.father_details?.email) ||
        (createPayload?.parent_details?.mother_details?.mobile &&
          createPayload?.parent_details?.mother_details?.email) ||
        (createPayload?.parent_details?.guardian_details?.mobile &&
          createPayload?.parent_details?.guardian_details?.email))
    ) {
      const response =
        await this.enquiryHelper.getDuplicateEnquiriesCountWhileCreate({
          ...createPayload,
          ...stages,
          ...defaultEnquiryFields,
        });

      if (response.duplicate > 0) {
        return {
          flag: 'duplicate',
          enquiry_id: response.result[0].projectedResults[0]._id.toString(),
          message: `This lead is been already created by ${response.result[0].projectedResults[0].assigned_to}
          with Enquiry No ${response.result[0].projectedResults[0].enquiry_number} for
          ${response.result[0].projectedResults[0].school_location.value}.
          if you click on continue, the owner of the existing lead will be replaced by yours.`,
        };
      }
    }

    let student_slug: string;
    if (
      createPayload?.other_details?.['enquiry_type'] ===
      ENQUIRY_TYPE.NEW_ADMISSION
    ) {
      student_slug = ENQUIRY_TYPE_SLUG.NEW_ADMISSION;
    } else if (
      createPayload?.other_details?.['enquiry_type'] === ENQUIRY_TYPE.KIDS_CLUB
    ) {
      student_slug = ENQUIRY_TYPE_SLUG.KIDS_CLUB;
    } else if (
      createPayload?.other_details?.['enquiry_type'] === ENQUIRY_TYPE.PSA
    ) {
      student_slug = ENQUIRY_TYPE_SLUG.PSA;
    }

    const responseStudentSlug = await this.mdmService.fetchDataFromAPI(
      `${MDM_API_URLS.STUDENT_TAGS}?filters[slug]=${student_slug}`,
    );
    if (responseStudentSlug?.data?.length) {
      createPayload.other_details = {
        ...other_details_,
        student_slug: [...responseStudentSlug.data],
      }
    };

    const enquiry: Partial<EnquiryDocument & Document> =
      await this.enquiryHelper.createEnquiry(
        createPayload,
        stages,
        defaultEnquiryFields,
      );
    if (enquiry.other_details?.['student_slug']?.length) {
      await this.mdmService.postDataToAPI(`${MDM_API_URLS.POST_STUDENT_TAGS}`, {
        data: {
          student_id: null,
          tag_id: enquiry.other_details?.['student_slug'][0]['id'],
          added_on: `${moment(enquiry['created_at']).format('YYYY-MM-DD')}`,
          removed_on: `${moment(enquiry['created_at']).add(1, 'y').format('YYYY-MM-DD')}`,
          is_verified: 1,
          enquiry_id: `${enquiry._id.toString()}`,
        },
      });
    }

    console.log('before notif call');
    this.loggerService.log(
      `notification sent to ${JSON.stringify(createPayload)}, data:: ${JSON.stringify(data)}`,
    );

    const globalIds = [];
    if (data?.parent_type === 'guardian') {
      globalIds.push(guardianGlobalId);
    } else if (data?.parent_type === 'father') {
      globalIds.push(fatherGlobalId);
    } else {
      globalIds.push(motherGlobalId);
    }

    // below function sends notification
    this.emailService.setEnquiryDetails(enquiry).sendNotification(
      EMAIL_TEMPLATE_SLUGS.ENQUIRY_CREATED,
      {
        enq_no: enquiry.enquiry_number,
        e_signature: '+91 6003000700',
        link: 'https://www.vibgyorhigh.com/',
        username: parentSsoUsername,
        password: parentSsoPassword,
      },
      [
        this.enquiryHelper.getEnquirerDetails(enquiry, 'email')
          ?.email as string,
      ],
    );

    // Create a new task
    const tPlusFiveDate = new Date();
    tPlusFiveDate.setDate(new Date().getDate() + 5);
    tPlusFiveDate.setHours(23, 59, 59, 999);

    await this.myTaskService.createMyTask({
      enquiry_id: enquiry._id.toString(),
      created_for_stage: ETaskEntityType.ENQUIRY,
      valid_from: new Date(),
      valid_till: tPlusFiveDate,
      task_creation_count: 1,
      assigned_to_id: enquiry.assigned_to_id,
    });
    if (isAnotherChild === 'yes') {
      const enquiryNumberAnotherChild = await this.enquiryHelper.getGlobalId(
        GLOBAL_ENQUIRY_GENERATOR_ID,
      );
      const anotherStudentPayload = {
        ...createPayload,
        enquiry_number: enquiryNumberAnotherChild,
        student_details: {
          first_name: anotherStudentDetail.first_name,
          last_name: anotherStudentDetail.last_name,
          dob: anotherStudentDetail.dob,
        },
      };
      await this.enquiryHelper.createEnquiry(
        anotherStudentPayload,
        stages,
        defaultEnquiryFields,
      );
    }
    return enquiry;
  }

  async update(
    enquiryId: string,
    payload: any,
    userInfo: CreatedByDetailsDto | null,
    req: Request,
  ) {
    const { metadata, data } = payload;
    const { form_id, enquiry_type_id } = metadata;
    const enquiryFormDetails = await this.enquiryHelper.getEnquiryFormDetails(
      form_id,
      req.headers.authorization,
    );
    const { inputs: formFields } = enquiryFormDetails;
    const validationObjects =
      this.enquiryHelper.extractValidationObjects(formFields);
    const errors = this.enquiryHelper.validateFormData(data, validationObjects);
    if (Object.keys(errors).length) {
      throw new HttpException('Invalid field value', HttpStatus.BAD_REQUEST, {
        cause: errors,
      });
    }

    const enquiryTypeDetails =
      await this.enquiryTypeService.getEnquiryTypeDetailsWithStageName(
        enquiry_type_id,
      );

    if (!enquiryTypeDetails) {
      throw new HttpException(
        'Invalid enquiry type sent',
        HttpStatus.BAD_REQUEST,
      );
    }
    const existingEnquiryDetails = await this.enquiryRepository.getById(
      new Types.ObjectId(enquiryId),
    );
    const paths = this.enquiryRepository.getSchemaPaths();
    const updatePayload = this.enquiryHelper.generateEnquirySchema(
      data,
      paths,
      existingEnquiryDetails,
    );
    updatePayload.enquiry_type_id = new Types.ObjectId(
      enquiry_type_id as string,
    );
    updatePayload.enquiry_form_id = new Types.ObjectId(form_id as string);

    const { updatedEnquiryStages, isRegistrationStageCompleted } =
      this.enquiryHelper.getUpdatedStatusOfEnquiryStage(
        enquiryTypeDetails,
        existingEnquiryDetails.enquiry_stages,
        form_id,
      );
    updatePayload.enquiry_stages = updatedEnquiryStages;

    if (
      updatePayload?.parent_details?.father_details?.mobile &&
      updatePayload?.parent_details?.father_details?.email
    ) {
      const response = await this.enquiryHelper.getParentGlobalId(
        updatePayload.parent_details.father_details.mobile,
        updatePayload.parent_details.father_details.email,
        updatePayload?.parent_details?.father_details?.first_name,
        updatePayload?.parent_details?.father_details?.last_name,
      );
      updatePayload.parent_details.father_details.global_id =
        response.global_no;
      updatePayload.parent_details.father_details.sso_username =
        response.sso_email;
      updatePayload.parent_details.father_details.sso_password =
        response.sso_password;
    }

    if (
      updatePayload?.parent_details?.mother_details?.mobile &&
      updatePayload?.parent_details?.mother_details?.email
    ) {
      const response = await this.enquiryHelper.getParentGlobalId(
        updatePayload.parent_details.mother_details.mobile,
        updatePayload.parent_details.mother_details.email,
        updatePayload?.parent_details?.mother_details?.first_name,
        updatePayload?.parent_details?.mother_details?.last_name,
      );
      updatePayload.parent_details.mother_details.global_id =
        response.global_no;
      updatePayload.parent_details.mother_details.sso_username =
        response.sso_email;
      updatePayload.parent_details.mother_details.sso_password =
        response.sso_password;
    }

    if (
      updatePayload?.parent_details?.guardian_details?.mobile &&
      updatePayload?.parent_details?.guardian_details?.email
    ) {
      const response = await this.enquiryHelper.getParentGlobalId(
        updatePayload.parent_details.guardian_details.mobile,
        updatePayload.parent_details.guardian_details.email,
        updatePayload?.parent_details?.guardian_details?.first_name,
        updatePayload?.parent_details?.guardian_details?.last_name,
      );
      updatePayload.parent_details.guardian_details.global_id =
        response.global_no;
      updatePayload.parent_details.guardian_details.sso_username =
        response.sso_email;
      updatePayload.parent_details.guardian_details.sso_password =
        response.sso_password;
    }

    const enquiry = await this.enquiryRepository.updateById(
      new Types.ObjectId(enquiryId),
      updatePayload,
    );

    if (isRegistrationStageCompleted) {
      const tPlusFiveDate = new Date();
      tPlusFiveDate.setDate(new Date().getDate() + 5);
      tPlusFiveDate.setHours(23, 59, 59, 999);

      await Promise.all([
        this.enquiryRepository.updateById(enquiry._id, {
          is_registered: true,
          registered_at: new Date(),
        }),
        this.myTaskService.createMyTask({
          enquiry_id: enquiry._id.toString(),
          created_for_stage: ETaskEntityType.REGISTRATION,
          task_creation_count: 1,
          valid_from: new Date(),
          valid_till: tPlusFiveDate,
          assigned_to_id: enquiry.assigned_to_id,
        }),
        this.enquiryLogService.createLog({
          enquiry_id: enquiry._id,
          event_type: EEnquiryEventType.REGISTRATION,
          event_sub_type: EEnquiryEventSubType.REGISTRATION_ACTION,
          event: EEnquiryEvent.REGISTRATION_DETAILS_RECIEVED,
          created_by: userInfo?.user_name ?? null,
          created_by_id: userInfo?.user_id ?? null,
        }),
      ]);
    }

    return enquiry;
  }

  async updateEnquiryData(enquiryId: string, payload: any) {
    const enquiryDetails = await this.enquiryRepository.updateById(
      new Types.ObjectId(enquiryId),
      payload,
    );
    return enquiryDetails;
  }

  async getEnquiryDetails(enquiryId: string) {
    const enquiryDetails = await this.enquiryRepository.getById(
      new Types.ObjectId(enquiryId),
    );
    const result = {
      ...enquiryDetails,
      ...(enquiryDetails?.other_details ? enquiryDetails?.other_details : {}),
    };
    delete result.other_details;
    return result;
  }

  async getEnquiryDetailsCC(
    req: Request,
    page?: number,
    size?: number,
    filtersArray?: FilterItemDto[],
    globalSearchText?: string,
  ) {
    const pageNumber = page || 1;
    const pageSize = size ? parseInt(size as any, 10) : 10;

    const createdByDetails = extractCreatedByDetailsFromBody(req);
    const { permissions } = await getSessionData(req, this.redisInstance);
    const isSuperAdmissionPermission = !!permissions.find(
      (permission) =>
        permission.toLowerCase() === ALL_LEADS_PERMISSION.toLowerCase(),
    );

    const skip = (pageNumber - 1) * pageSize;
    const { user_id } = createdByDetails;
    let customFilter = {};

    let isStatusFilterApplied = false;

    filtersArray &&
      filtersArray.forEach((filter) => {
        const { column, operation, search } = filter;
        const filterClause = buildFilter(column, operation, search);
        if (column === 'status') {
          isStatusFilterApplied = true;
        }
        customFilter = { ...customFilter, ...filterClause };
      });

    const stages = await this.enquiryStageRepository.getMany(
      {
        name: {
          $in: ENQUIRY_STAGES,
        },
      },
      { name: 1 },
    );

    const pipeline: PipelineStage[] = [
      {
        $match: {
          ...(!isSuperAdmissionPermission ? { assigned_to_id: user_id } : {}),
          is_registered: false,
        },
      },
      {
        $sort: {
          updated_at: -1,
        },
      },
      {
        $lookup: {
          from: 'enquiryType',
          localField: 'enquiry_type_id',
          foreignField: '_id',
          as: 'enquiryType',
        },
      },
      {
        $addFields: {
          enquiry_type: {
            $arrayElemAt: ['$enquiryType', 0],
          },
        },
      },
      {
        $lookup: {
          from: 'followUps',
          localField: '_id',
          foreignField: 'enquiry_id',
          as: 'enquiryFollowUps',
        },
      },
      {
        $addFields: {
          stages: stages,
        },
      },
      {
        $addFields: {
          completedStages: {
            $filter: {
              input: '$enquiry_stages',
              as: 'stage',
              cond: {
                $or: [
                  {
                    $and: [
                      { $eq: ['$$stage.stage_name', 'Enquiry'] },
                      {
                        $eq: ['$$stage.status', EEnquiryStageStatus.INPROGRESS],
                      },
                    ],
                  },
                  { $eq: ['$$stage.status', EEnquiryStageStatus.COMPLETED] },
                  { $eq: ['$$stage.status', EEnquiryStageStatus.PASSED] },
                  { $eq: ['$$stage.status', EEnquiryStageStatus.APPROVED] },
                ],
              },
            },
          },
        },
      },
      {
        $addFields: {
          lastCompletedStage: {
            $arrayElemAt: [
              '$completedStages',
              { $subtract: [{ $size: '$completedStages' }, 1] },
            ],
          },
        },
      },
      {
        $addFields: {
          lastCompletedStageIndex: {
            $indexOfArray: [
              '$enquiry_stages.stage_name',
              '$lastCompletedStage.stage_name',
            ],
          },
        },
      },
      {
        $addFields: {
          nextStage: {
            $arrayElemAt: [
              '$enquiry_stages',
              { $add: ['$lastCompletedStageIndex', 1] },
            ],
          },
        },
      },
      {
        $addFields: {
          enquiryFor: {
            $ifNull: ['$enquiry_type.name', null],
          },
          lastCompletedStage: { $arrayElemAt: ['$completedStages', -1] },
          studentName: {
            $concat: [
              { $ifNull: ['$student_details.first_name', ''] },
              ' ',
              { $ifNull: ['$student_details.last_name', ''] },
            ],
          },
          mobileNumber: {
            $switch: {
              branches: [
                {
                  case: {
                    $eq: ['$other_details.parent_type', EParentType.FATHER],
                  },
                  then: '$parent_details.father_details.mobile',
                },
                {
                  case: {
                    $eq: ['$other_details.parent_type', EParentType.MOTHER],
                  },
                  then: '$parent_details.mother_details.mobile',
                },
                {
                  case: {
                    $eq: ['$other_details.parent_type', EParentType.GUARDIAN],
                  },
                  then: '$parent_details.guardian_details.mobile',
                },
              ],
              default: null,
            },
          },
          grade: {
            $ifNull: ['$student_details.grade.value', null],
          },
          board: {
            $ifNull: ['$board.value', null],
          },
          nextFollowUpDate: {
            $cond: {
              if: { $eq: [{ $type: '$next_follow_up_at' }, 'date'] },
              then: {
                $dateToString: {
                  format: '%d-%m-%Y',
                  date: '$next_follow_up_at',
                },
              },
              else: null,
            },
          },
          enquiryDate: {
            $dateToString: {
              format: '%d-%m-%Y',
              date: '$created_at',
            },
          },
          enquirer: {
            $switch: {
              branches: [
                {
                  case: { $eq: ['$other_details.parent_type', 'Father'] },
                  then: {
                    $concat: [
                      {
                        $ifNull: [
                          '$parent_details.father_details.first_name',
                          '',
                        ],
                      },
                      ' ',
                      {
                        $ifNull: [
                          '$parent_details.father_details.last_name',
                          '',
                        ],
                      },
                    ],
                  },
                },
                {
                  case: { $eq: ['$other_details.parent_type', 'Mother'] },
                  then: {
                    $concat: [
                      {
                        $ifNull: [
                          '$parent_details.mother_details.first_name',
                          '',
                        ],
                      },
                      ' ',
                      {
                        $ifNull: [
                          '$parent_details.mother_details.last_name',
                          '',
                        ],
                      },
                    ],
                  },
                },
                {
                  case: { $eq: ['$other_details.parent_type', 'Guardian'] },
                  then: {
                    $concat: [
                      {
                        $ifNull: [
                          '$parent_details.guardian_details.first_name',
                          '',
                        ],
                      },
                      ' ',
                      {
                        $ifNull: [
                          '$parent_details.guardian_details.last_name',
                          '',
                        ],
                      },
                    ],
                  },
                },
              ],
              default: null,
            },
          },
          priority: {
            $cond: [
              {
                $and: [
                  { $ne: ['$status', EEnquiryStatus.CLOSED] }, // Check if status is NOT 'closed'
                  {
                    $lte: [
                      {
                        $dateDiff: {
                          startDate: '$created_at',
                          endDate: '$$NOW',
                          unit: 'day',
                        },
                      },
                      15,
                    ],
                  },
                ],
              },
              `${ENQUIRY_PRIORITY.HOT}`,
              {
                $cond: [
                  {
                    $and: [
                      { $ne: ['$status', EEnquiryStatus.CLOSED] },
                      {
                        $gt: [
                          {
                            $dateDiff: {
                              startDate: '$created_at',
                              endDate: '$$NOW',
                              unit: 'day',
                            },
                          },
                          15,
                        ],
                      },
                      {
                        $lte: [
                          {
                            $dateDiff: {
                              startDate: '$created_at',
                              endDate: '$$NOW',
                              unit: 'day',
                            },
                          },
                          30,
                        ],
                      },
                    ],
                  },
                  `${ENQUIRY_PRIORITY.WARM}`,
                  {
                    $cond: [
                      { $eq: ['$status', EEnquiryStatus.CLOSED] }, // If status is 'closed', set to COLD
                      `${ENQUIRY_PRIORITY.COLD}`,
                      `${ENQUIRY_PRIORITY.COLD}`, // Default to COLD if no other condition matches
                    ],
                  },
                ],
              },
            ],
          },
          school: {
            $ifNull: ['$school_location.value', null],
          },
          academicYear: {
            $ifNull: ['$academic_year.value', null],
          },
          nextAction: 'NA',
        },
      },
      {
        $project: {
          _id: 0,
          id: '$_id',
          enquiryFor: 1,
          studentName: 1,
          mobileNumber: 1,
          grade: 1,
          board: 1,
          stage: '$lastCompletedStage.stage_name',
          nextFollowUpDate: '$nextFollowUpDate',
          next_follow_up_at: 1,
          nextAction: '$nextStage.stage_name',
          actionDate: 1,
          enquiryDate: 1,
          enquirer: 1,
          status: 1,
          priority: 1,
          school: 1,
          created_at: 1,
          academicYear: 1,
          enquiry_number: 1,
          leadOwner: '$assigned_to',
          enquirySource: '$enquiry_source.value',
          isNewLead: {
            $cond: {
              if: { $gt: [{ $size: '$enquiryFollowUps' }, 0] },
              then: false,
              else: true,
            },
          },
        },
      },
      {
        $facet: {
          data: [
            {
              $skip: skip,
            },
            {
              $limit: pageSize,
            },
            {
              $project: {
                created_at: 0,
                next_follow_up_at: 0,
              },
            },
          ],
          totalCount: [
            {
              $count: 'count',
            },
          ],
        },
      },
    ];

    // if (!isStatusFilterApplied) {
    //   pipeline.unshift({
    //     $match: {
    //       status: { $nin: [EEnquiryStatus.CLOSED, EEnquiryStatus.ADMITTED] }, // Add at the start
    //     },
    //   });
    // }

    if (Object.keys(customFilter).length) {
      // Adding the match condition before last stage (facet stage)
      pipeline.splice(pipeline.length - 1, 0, {
        $match: {
          ...customFilter,
        },
      });
    } else if (globalSearchText) {
      pipeline.shift();
      pipeline.splice(pipeline.length - 1, 0, {
        $match: {
          $or: enquiryGlobalSearchFields.map((searchField) => {
            return {
              [searchField]: { $regex: globalSearchText, $options: 'i' },
            };
          }),
        },
      });
    }

    console.log('Pipeline ---> ', JSON.stringify(pipeline));

    const populatedEnquiries = await this.enquiryRepository
      .aggregate(pipeline)
      .exec();

    const [result] = populatedEnquiries;
    const paginatedData = result.data;
    const totalCount =
      result.totalCount.length > 0 ? result.totalCount[0].count : 0;

    const totalPages = Math.ceil(totalCount / pageSize);

    return {
      content: paginatedData,
      pagination: {
        total_pages: totalPages,
        page_size: pageSize,
        total_count: totalCount,
      },
    };
  }

  // Encrypt a message using the public key
  encryptData(data: string): string {
    // Read the public key from the file
    const publicKey = fs.readFileSync('public-key.pem', 'utf8');

    const buffer = Buffer.from(data, 'utf8');
    const encrypted = crypto.publicEncrypt(
      {
        key: publicKey.replace(/\\n/g, '\n'),
        padding: crypto.constants.RSA_PKCS1_OAEP_PADDING,
      },
      buffer,
    );
    return encrypted.toString('base64');
  }

  // Decrypt a message using the private key
  decryptData(encryptedData: string): string {
    // Read the private key from the file
    const privateKey = fs.readFileSync('private-key.pem', 'utf8');

    const buffer = Buffer.from(encryptedData, 'base64');
    const decrypted = crypto.privateDecrypt(
      {
        key: privateKey.replace(/\\n/g, '\n'),
        padding: crypto.constants.RSA_PKCS1_OAEP_PADDING,
      },
      buffer,
    );
    return decrypted.toString('utf8');
  }

  async uploadEnquiryDocument(
    req: Request,
    enquiryId: string,
    documentId: number,
    document: Express.Multer.File,
  ) {
    const bucketName = this.configService.get<string>('BUCKET_NAME');
    const folderName = this.configService.get<string>('FOLDER_NAME');

    await this.storageService.setStorage(EStorageType.GCS, {
      projectId: this.configService.get<string>('PROJECT_ID'),
      credentials: {
        type: this.configService.get<string>('TYPE'),
        project_id: this.configService.get<string>('PROJECT_ID'),
        private_key_id: this.configService.get<string>('PRIVATE_KEY_ID'),
        private_key: this.configService
          .get<string>('PRIVATE_KEY')
          .replace(/\\n/g, '\n'),
        client_email: this.configService.get<string>('CLIENT_EMAIL'),
        client_id: this.configService.get<string>('GCS_CLIENT_ID'),
        auth_uri: this.configService.get<string>('AUTH_URI'),
        token_uri: this.configService.get<string>('TOKEN_URI'),
        auth_provider_x509_cert_url: this.configService.get<string>(
          'AUTH_PROVIDER_X509_CERT_URL',
        ),
        client_x509_cert_url: this.configService.get<string>(
          'CLIENT_X509_CERT_URL',
        ),
        universe_domain: this.configService.get<string>('UNIVERSAL_DOMAIN'),
      },
      bucketName: bucketName,
      folderName: folderName,
    });

    const uploadedFileName = await this.storageService.uploadFile(document);

    if (!uploadedFileName) {
      throw new HttpException(
        'Something went wrong while uploading file!',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    const enquiryDetails = await this.enquiryRepository.getById(
      new Types.ObjectId(enquiryId),
    );
    if (!enquiryDetails) {
      throw new HttpException(
        'Enquiry details not found',
        HttpStatus.NOT_FOUND,
      );
    }

    const { documents, enquiry_stages, status } = enquiryDetails as any;

    const updatedDocuments = JSON.parse(JSON.stringify(documents));
    const documentToBeUploaded = updatedDocuments.find(
      (document) => document.document_id === documentId,
    );
    if (!documentToBeUploaded) {
      throw new HttpException('Unknown document', HttpStatus.NOT_FOUND);
    }

    documentToBeUploaded.file = uploadedFileName;
    const loggedInUserRoles =
      await this.enquiryHelper.getCurrentUserRoleCode(req);

    let documentIsVerified = false;
    const { CC, RE } = getCcReHrisCodes();
    const isCcReRole = [...CC, ...RE].some((role) =>
      loggedInUserRoles.includes(role),
    );
    if (isCcReRole) {
      documentToBeUploaded.is_verified = true;
      documentIsVerified = true;
    }

    await this.enquiryRepository.updateById(new Types.ObjectId(enquiryId), {
      documents: updatedDocuments,
    });

    const admissionType = this.enquiryHelper.getAdmissionType(updatedDocuments);
    const admissionTypeStageRegex = new RegExp(
      'Admitted or Provisional Approval',
      'i',
    );
    const admissionTypeStage = enquiry_stages.find((stage) =>
      admissionTypeStageRegex.test(stage.stage_name),
    );
    if (
      admissionType === EEnquiryAdmissionType.ADMISSION &&
      (admissionTypeStage.status !== EEnquiryStageStatus.INPROGRESS ||
        admissionTypeStage.status !== EEnquiryStageStatus.OPEN)
    ) {
      const updatedStages = enquiry_stages.map((stage) => {
        if (admissionTypeStageRegex.test(stage.stage_name)) {
          stage.status = EEnquiryStageStatus.ADMITTED;
        }
        return stage;
      });
      await this.enquiryRepository.updateById(new Types.ObjectId(enquiryId), {
        enquiry_stages: updatedStages,
      });
    }

    // Push the newly uploaded documents to academics if the admission is completed
    if (status === EEnquiryStatus.ADMITTED) {
      const [updatedEnquiryDetails, admissionDetails] = await Promise.all([
        this.enquiryRepository.getById(new Types.ObjectId(enquiryId)),
        this.admissionRepository.getOne({
          enquiry_id: new Types.ObjectId(enquiryId),
        }),
      ]);
      if (admissionDetails?.student_id) {
        await this.axiosService
          .setBaseUrl(this.configService.get<string>('ADMIN_PANEL_URL'))
          .setUrl(ADMIN_API_URLS.MAP_STUDENT_DOCUMENTS)
          .setMethod(EHttpCallMethods.POST)
          .setHeaders({
            Authorization: req.headers.authorization,
          } as AxiosRequestHeaders)
          .setBody({
            student_id: admissionDetails.student_id,
            documents: updatedEnquiryDetails.documents,
          })
          .sendRequest();
      }
      this.loggerService.log(
        `New uploaded documents pushed to academics against student Id - ${admissionDetails.student_id}`,
      );
    }
    return { documentIsVerified };
  }

  async getUploadedDocumentUrl(
    enquiryId: string,
    documentId: number,
    download = false,
  ) {
    const enquiryDetails = await this.enquiryRepository.getById(
      new Types.ObjectId(enquiryId),
    );
    if (!enquiryDetails) {
      throw new HttpException('Enquiry not found', HttpStatus.NOT_FOUND);
    }

    const { documents } = enquiryDetails;
    const document = documents.find(
      (document) => document.document_id === documentId,
    );

    if (!document) {
      throw new HttpException('Document not found', HttpStatus.NOT_FOUND);
    }

    const { file } = document;
    const bucketName = this.configService.get<string>('BUCKET_NAME');
    const folderName = this.configService.get<string>('FOLDER_NAME');

    await this.storageService.setStorage(EStorageType.GCS, {
      projectId: this.configService.get<string>('PROJECT_ID'),
      credentials: {
        type: this.configService.get<string>('TYPE'),
        project_id: this.configService.get<string>('PROJECT_ID'),
        private_key_id: this.configService.get<string>('PRIVATE_KEY_ID'),
        private_key: this.configService
          .get<string>('PRIVATE_KEY')
          .replace(/\\n/g, '\n'),
        client_email: this.configService.get<string>('CLIENT_EMAIL'),
        client_id: this.configService.get<string>('GCS_CLIENT_ID'),
        auth_uri: this.configService.get<string>('AUTH_URI'),
        token_uri: this.configService.get<string>('TOKEN_URI'),
        auth_provider_x509_cert_url: this.configService.get<string>(
          'AUTH_PROVIDER_X509_CERT_URL',
        ),
        client_x509_cert_url: this.configService.get<string>(
          'CLIENT_X509_CERT_URL',
        ),
        universe_domain: this.configService.get<string>('UNIVERSAL_DOMAIN'),
      },
      bucketName: bucketName,
      folderName: folderName,
    });

    const signedUrl = await this.storageService.getSignedUrl(
      bucketName,
      file,
      download,
    );
    return { url: signedUrl };
  }

  async getEnquiryTimeline(
    enquiryId: string,
    filters?: { eventType?: string; eventSubType?: string },
  ) {
    const timelineFilters = {
      eventType: Object.values(EEnquiryEventType),
    };
    if (filters) {
      const timeline = await this.enquiryLogService.getEnquiryLogsByEnquiryId(
        enquiryId,
        'asc',
        filters,
      );
      return {
        filters: timelineFilters,
        timeline: this.enquiryHelper.getDetailedEnquiryTimeline(timeline),
      };
    }
    const timeline = await this.enquiryLogService.getEnquiryLogsByEnquiryId(
      enquiryId,
      'asc',
    );
    return {
      filters: timelineFilters,
      timeline: this.enquiryHelper.getDetailedEnquiryTimeline(timeline),
    };
  }

  async getTimeLineEventSubTypes(eventType: EEnquiryEventType) {
    switch (eventType) {
      case EEnquiryEventType.ENQUIRY:
        return [
          EEnquiryEvent.ENQUIRY_CREATED,
          EEnquiryEvent.ENQUIRY_CLOSED,
          EEnquiryEvent.ENQUIRY_MERGED,
          EEnquiryEvent.ENQUIRY_TRANSFERRED,
          EEnquiryEvent.ENQUIRY_REOPENED,
          EEnquiryEvent.ENQUIRY_REASSIGNED,
        ];
      case EEnquiryEventType.SCHOOL_TOUR:
        return [
          EEnquiryEvent.SCHOOL_TOUR_SCHEDULED,
          EEnquiryEvent.SCHOOL_TOUR_RESCHEDULE,
          EEnquiryEvent.SCHOOL_TOUR_CANCELLED,
          EEnquiryEvent.SCHOOL_TOUR_COMPLETED,
        ];
      case EEnquiryEventType.REGISTRATION:
        return [
          EEnquiryEvent.REGISTRATION_DETAILS_RECIEVED,
          EEnquiryEvent.REGISTRATION_FEE_RECEIVED,
          EEnquiryEvent.REGISTRATION_FEE_REQUEST_SENT,
        ];
      case EEnquiryEventType.COMPETENCY_TEST:
        return [
          EEnquiryEvent.COMPETENCY_TEST_SCHEDULED,
          EEnquiryEvent.COMPETENCY_TEST_RESCHEDULED,
          EEnquiryEvent.COMPETENCY_TEST_CANCELLED,
          EEnquiryEvent.COMPETENCY_TEST_PASSED,
          EEnquiryEvent.COMPETENCY_TEST_FAILED,
        ];
      case EEnquiryEventType.ADMISSION:
        return [
          EEnquiryEvent.ADMISSION_APPROVED,
          EEnquiryEvent.ADMISSION_COMPLETED,
          EEnquiryEvent.ADMISSION_REJECTED,
          EEnquiryEvent.PAYMENT_RECEIVED,
          EEnquiryEvent.SUBJECTS_SELECTED,
          EEnquiryEvent.FEES_ATTACHED,
          EEnquiryEvent.VAS_ADDED,
        ];
      case EEnquiryEventType.FOLLOW_UP:
        return [
          EEnquiryEvent.FOLLOW_UP_CALL,
          EEnquiryEvent.FOLLOW_UP_EMAIL,
          EEnquiryEvent.FOLLOW_UP_PHYSICAL_MEETING,
          EEnquiryEvent.FOLLOW_UP_VIRTUAL_MEETING,
        ];
    }
  }

  async getMergedEnquiry(enquiryDoc: Partial<EnquiryDocument & Document>) {
    const mergeEnquiryDoc = await this.enquiryRepository.aggregate([
      {
        $match: {
          parent_enquiry_number: {
            $exists: true,
            $ne: null,
            $eq: enquiryDoc.enquiry_number,
          },
        },
      },
    ]);
    return mergeEnquiryDoc;
  }

  async getMergedEnquiries(enquiryId: string) {
    const enquiryDetails = await this.enquiryRepository.getById(
      new Types.ObjectId(enquiryId),
    );
    const mergedDoc = await this.getMergedEnquiry(enquiryDetails);
    const result = mergedDoc.map((data) => {
      const enquiryName = `${data.student_details.first_name} ${data.student_details.last_name} ( ${data.enquiry_number} ) <= ${enquiryDetails.student_details.first_name} ${enquiryDetails.student_details.last_name} ( ${enquiryDetails.enquiry_number} )`;
      return {
        ...data,
        enquiry_name: enquiryName,
      };
    });
    return result;
  }

  async getSimilarEnquiries(enquiryId: string, user_id?: number) {
    const enquiryDetails = await this.enquiryRepository.aggregate([
      {
        $match: {
          _id: new Types.ObjectId(enquiryId),
        },
      },
      {
        $lookup: {
          from: 'enquiryType',
          localField: 'enquiry_type_id',
          foreignField: '_id',
          as: 'enquiry_type_details',
          pipeline: [
            {
              $project: {
                name: 1,
                stages: 1,
              },
            },
          ],
        },
      },
    ]);
    if (!enquiryDetails.length) {
      throw new HttpException('Enquiry not found', HttpStatus.NOT_FOUND);
    }

    const {
      _id,
      other_details,
      parent_details,
      enquiry_type_id,
      enquiry_date,
      status,
      enquiry_type_details,
      enquiry_stages,
      created_at,
    } = enquiryDetails[0];

    const { stages: enquiryTypeStages, name: enquiryType } =
      enquiry_type_details[0];

    let formCompletedPercentage = 0;
    enquiry_stages.forEach((enquiryStage) => {
      enquiryTypeStages.forEach((enquiryTypeStage) => {
        if (
          enquiryStage.stage_id.toString() ===
            enquiryTypeStage.stage_id.toString() &&
          (enquiryStage.status === EEnquiryStageStatus.COMPLETED ||
            enquiryStage.status === EEnquiryStageStatus.PASSED ||
            enquiryStage.status === EEnquiryStageStatus.APPROVED)
        ) {
          formCompletedPercentage += enquiryTypeStage.weightage;
        }
      });
    });
    const response = {
      duplicate_count: 0,
      merged_count: 0, // TODO: Add the logic to find merged count
      form_completed_percentage: formCompletedPercentage,
      enquiry_status: status,
      enquiry_type: enquiryType,
    };

    if (status === EEnquiryStatus.OPEN) {
      const duplicateCount =
        await this.enquiryHelper.getDuplicateEnquiriesCount(enquiryDetails[0]);
      response.duplicate_count = duplicateCount;
    }

    if (status === EEnquiryStatus.OPEN) {
      const mergedCount = await this.getMergedEnquiry(enquiryDetails[0]);
      response.merged_count = mergedCount?.length ? mergedCount?.length : 0;
    }

    const pipeline = [];
    switch ((other_details as any)?.parent_type?.toLowerCase()) {
      case 'father':
        if (parent_details?.father_details?.global_id) {
          pipeline.push({
            $match: {
              'parent_details.father_details.global_id':
                parent_details?.father_details?.global_id,
            },
          });
        }
        response['current_enquiry_details'] = {
          enquirer_parent: 'Father',
          enquiry_type_id: enquiry_type_id,
          enquiry_name: this.enquiryHelper.getSimilarEnquiryName(
            enquiryDetails[0],
          ),
          enquiry_id: _id,
          academic_year: enquiryDetails[0]?.academic_year ?? null,
          enquirer_name:
            parent_details?.father_details?.first_name +
            parent_details?.father_details?.last_name,
          enquirer_email: parent_details?.father_details?.email,
          enquiry_date: formatToTimeZone(
            created_at,
            'YYYY-MM-DDTHH:mm:ss.sss',
            { timeZone: 'Asia/Kolkata' },
          ),
        };
        break;
      case 'mother':
        if (parent_details?.mother_details?.global_id) {
          pipeline.push({
            $match: {
              'parent_details.mother_details.global_id':
                parent_details?.mother_details?.global_id,
            },
          });
        }
        response['current_enquiry_details'] = {
          enquirer_parent: 'Mother',
          enquiry_type_id: enquiry_type_id,
          enquiry_name: this.enquiryHelper.getSimilarEnquiryName(
            enquiryDetails[0],
          ),
          enquiry_id: _id,
          academic_year: enquiryDetails[0]?.academic_year ?? null,
          enquirer_name:
            parent_details?.mother_details?.first_name +
            parent_details?.mother_details?.last_name,
          enquirer_email: parent_details?.mother_details?.email,
          enquiry_date: enquiry_date,
        };
        break;
      case 'guardian':
        if (parent_details?.guardian_details?.global_id) {
          pipeline.push({
            $match: {
              'parent_details.guardian_details.global_id':
                parent_details?.guardian_details?.global_id,
            },
          });
        }
        response['current_enquiry_details'] = {
          enquirer_parent: 'Guardian',
          enquiry_type_id: enquiry_type_id,
          enquiry_name: this.enquiryHelper.getSimilarEnquiryName(
            enquiryDetails[0],
          ),
          enquiry_id: _id,
          academic_year: enquiryDetails[0]?.academic_year ?? null,
          enquirer_name:
            parent_details?.guardian_details?.first_name +
            parent_details?.guardian_details?.last_name,
          enquirer_email: parent_details?.guardian_details?.email,
          enquiry_date: enquiry_date,
        };
        break;
    }
    pipeline.push({
      $match: {
        _id: {
          $ne: _id,
        },
        status: EEnquiryStatus.OPEN,
      },
    });
    if (user_id) {
      pipeline[0]['$match']['assigned_to_id'] = user_id;
    }
    pipeline.push({
      $lookup: {
        from: 'enquiryType',
        localField: 'enquiry_type_id',
        foreignField: '_id',
        as: 'enquiry_type_details',
        pipeline: [
          {
            $project: {
              name: 1,
            },
          },
        ],
      },
    });

    const similarEnquires: Partial<EnquiryDocument & Document>[] =
      await this.enquiryRepository.aggregate(pipeline);
    if (similarEnquires.length) {
      response['similar_enquiries'] = similarEnquires.map(
        (enquiry: Partial<EnquiryDetails>) => {
          let isGlobalIdExists = false;
          if (
            enquiry?.parent_details?.father_details?.global_id ||
            enquiry?.parent_details?.mother_details?.global_id ||
            enquiry?.parent_details?.guardian_details?.global_id
          ) {
            isGlobalIdExists = true;
          }
          const similarEnquiry = {
            enquiry_type_id: enquiry.enquiry_type_id,
            enquiry_name: this.enquiryHelper.getSimilarEnquiryName(enquiry),
            enquiry_id: enquiry._id,
            academic_year: enquiry?.academic_year ?? null,
            enquirer_name:
              enquiry?.parent_details?.guardian_details?.first_name +
              enquiry?.parent_details?.guardian_details?.last_name,
            enquirer_email:
              enquiry?.parent_details?.guardian_details?.email ?? null,
            enquiry_date: enquiry.enquiry_date,
          };
          switch (enquiry?.other_details?.['parent_type']?.toLowerCase()) {
            case 'father':
              similarEnquiry['enquirer_parent'] = 'Father';
              break;
            case 'mother':
              similarEnquiry['enquirer_parent'] = 'Mother';
              break;
            case 'mother':
              similarEnquiry['enquirer_parent'] = 'Guardian';
              break;
            default:
              similarEnquiry['enquirer_parent'] = 'N/A';
          }
          if (isGlobalIdExists) {
            return similarEnquiry;
          }
        },
      );
    } else {
      response['similar_enquiries'] = [];
    }
    return response;
  }

  async getEnquirerDetails(
    enquiryId: string,
    pageSize: number,
    pageNumber: number,
  ) {
    const enquiryDetails = await this.enquiryRepository.getById(
      new Types.ObjectId(enquiryId),
    );
    if (!enquiryDetails) {
      throw new HttpException(
        'Enquiry details not found',
        HttpStatus.NOT_FOUND,
      );
    }

    const enquirerDetails: Record<string, unknown> = {};
    const { father_details, mother_details, guardian_details } =
      enquiryDetails.parent_details;
    const pipeline = [];
    switch ((enquiryDetails.other_details as any).parent_type) {
      case EParentType.FATHER:
        pipeline.push({
          $match: {
            'parent_details.father_details.global_id': father_details.global_id,
          },
        });
        enquirerDetails['name'] =
          father_details.first_name + father_details.last_name;
        enquirerDetails['email'] = father_details.email;
        enquirerDetails['mobile'] = father_details.mobile;
        break;
      case EParentType.MOTHER:
        pipeline.push({
          $match: {
            'parent_details.mother_details.global_id': mother_details.global_id,
          },
        });
        enquirerDetails['name'] =
          mother_details.first_name + mother_details.last_name;
        enquirerDetails['email'] = mother_details.email;
        enquirerDetails['mobile'] = mother_details.mobile;
        break;
      case EParentType.GUARDIAN:
        pipeline.push({
          $match: {
            'parent_details.guardian_details.global_id':
              guardian_details.global_id,
          },
        });
        enquirerDetails['name'] =
          guardian_details.first_name + guardian_details.last_name;
        enquirerDetails['email'] = guardian_details.email;
        enquirerDetails['mobile'] = guardian_details.mobile;
        break;
    }

    pipeline.push(
      {
        $lookup: {
          from: 'enquiryStage',
          localField: 'enquiry_stages.stage_id',
          foreignField: '_id',
          as: 'stages',
        },
      },
      {
        $addFields: {
          completedStages: {
            $filter: {
              input: '$enquiry_stages',
              as: 'stage',
              cond: {
                $or: [
                  { $eq: ['$$stage.status', EEnquiryStageStatus.COMPLETED] },
                  { $eq: ['$$stage.status', EEnquiryStageStatus.PASSED] },
                  { $eq: ['$$stage.status', EEnquiryStageStatus.APPROVED] },
                ],
              },
            },
          },
        },
      },
      {
        $addFields: {
          lastCompletedStage: { $arrayElemAt: ['$completedStages', -1] },
        },
      },
      {
        $sort: {
          created_at: -1,
        },
      },
      {
        $project: {
          enquiry_id: '$_id',
          enquiry_for: '$other_details.enquiry_type',
          enquiry_number: '$enquiry_number',
          enquiry_date: 1,
          enquiry_type: '$other_details.enquiry_type',
          school_name: '$school_location.value',
          student_first_name: '$student_details.first_name',
          student_last_name: '$student_details.last_name',
          stages: '$enquiry_stages',
          lastCompletedStage: 1,
        },
      },
    );

    const enquiries = await this.enquiryRepository.aggregate(
      mongodbPaginationQuery(pipeline, { pageNumber, pageSize }),
    );
    const similarEnquires = enquiries[0].data.map((enquiry) => {
      return {
        enquiry_id: enquiry?.enquiry_id ?? 'N/A',
        enquiry_date: enquiry?.enquiry_date ?? 'N/A',
        enquiry_for: enquiry?.enquiry_for ?? 'N/A',
        enquiry_number: enquiry?.enquiry_number ?? 'N/A',
        school_name: enquiry?.school_name ?? 'N/A',
        student_name:
          (enquiry?.student_first_name ?? '') +
          (enquiry?.student_last_name ?? ''),
        stage_name: enquiry.stages
          ? (enquiry.stages.find(
              (stage) => stage.status === EEnquiryStageStatus.OPEN,
            )?.stage_name ??
            enquiry?.lastCompletedStage?.stage_name ??
            'N/A')
          : 'N/A',
        stage_status: enquiry.stages
          ? (enquiry?.stages.find(
              (stage) => stage.status === EEnquiryStageStatus.OPEN,
            )?.status ??
            enquiry?.lastCompletedStage?.status ??
            'N/A')
          : 'N/A',
      };
    });
    return {
      enquirerDetails,
      similarEnquiries: {
        ...enquiries[0],
        currentPage: pageNumber,
        pageSize: pageSize,
        data: similarEnquires,
      },
    };
  }

  async getEnquiryTransferDetails(enquiryId: string) {
    const enquiryDetails = await this.enquiryRepository.aggregate([
      {
        $match: {
          _id: new Types.ObjectId(enquiryId),
        },
      },
      {
        $lookup: {
          from: 'enquiryType',
          localField: 'enquiry_type_id',
          foreignField: '_id',
          as: 'enquiry_type_details',
          pipeline: [
            {
              $project: {
                name: 1,
              },
            },
          ],
        },
      },
      {
        $project: {
          _id: 1,
          student_first_name: '$student_details.first_name',
          student_last_name: '$student_details.last_name',
          status: 1,
          school_location: '$school_location.value',
          enquiry_number: '$enquiry_number',
          enquiry_type: '$enquiry_type_details.name',
          parent_type: '$other_details.parent_type',
          parent_details: 1,
        },
      },
    ]);

    const { parent_details, parent_type, _id } = enquiryDetails[0];

    const similarEnquiries =
      await this.enquiryHelper.getSimilarEnquiriesByEnquirerGlobalId(
        parent_details,
        parent_type,
        _id,
      );

    delete enquiryDetails[0].parent_details;

    const result: any = {
      enquiryDetails: {
        ...enquiryDetails[0],
        enquiry_type: enquiryDetails[0].enquiry_type[0],
      },
      similarEnquiries: [],
    };
    if (similarEnquiries.length) {
      result.similarEnquiries = similarEnquiries.map((enquiry) => {
        return {
          enquiry_id: enquiry._id,
          enquiry_date: enquiry.enquiry_date,
          enquiry_number: enquiry.enquiry_number,
          enquiry_for: enquiry.other_details.enquiry_type,
          student_name:
            (enquiry?.student_details?.first_name ?? '') +
            ' ' +
            (enquiry?.student_details?.last_name ?? ''),
          stage_name: enquiry.stages
            ? enquiry.stages.find((stage) => stage.status === 'Open').stage_name
            : 'N/A',
          stage_status: enquiry.stages
            ? enquiry?.stages.find((stage) => stage.status === 'Open').status
            : 'N/A',
        };
      });
    }
    return result;
  }

  async transfer(
    enquiryIds: string[],
    schoolLocationDetails: MasterFieldDto,
    req: Request,
  ) {
    const schoolDetails = await this.mdmService.fetchDataFromAPI(
      `${MDM_API_URLS.SCHOOL}/${schoolLocationDetails.id}`,
    );
    const REHrisCode = getCcReHrisCodes().RE;
    if (!REHrisCode.length) {
      throw new BadRequestException('No employee');
    }

    const filter: (string | number)[][] = REHrisCode.map((data) => {
      return [
        'filters[$and][1][hr_hris_unique_role][HRIS_Unique_Role_Code][$in]',
        data,
      ];
    });

    const schoolCode = schoolDetails?.data?.attributes?.code;
    filter.push([
      'filters[$and][0][Base_Location][Base_Location][parent1_id][$eq]',
      schoolCode,
    ]);

    [
      EMPLOYEE_ACTIVITY_STATUS.ACTIVE,
      EMPLOYEE_ACTIVITY_STATUS.SERVING_NOTICE_PERIOD,
    ].forEach((status, index) => {
      filter.push([`filters[Employment_Status][id][$in][${index}]`, status]);
    });
    const response = await this.mdmService.fetchDataFromAPI(
      `/api/hr-employee-masters`,
      [...filter],
    );
    this.loggerService.log(
      `Response api/hr-employee-master :::: payload:: ${JSON.stringify(filter)} and response:: ${JSON.stringify(response)}`,
    );

    if (!response?.data?.length) {
      throw new HttpException(
        'Employee list not found against the given role',
        HttpStatus.NOT_FOUND,
      );
    }

    const employeeIds = response?.data.map((employeeData) => employeeData.id);

    const pipeline: PipelineStage[] = [
      {
        $match: {
          assigned_to_id: {
            $in: employeeIds,
          },
        },
      },
      {
        $sort: {
          created_at: -1,
        },
      },
    ];

    const enquiryData = await this.enquiryRepository.aggregate(pipeline);
    const enquiryIdArr = enquiryIds.map((id) => new Types.ObjectId(id));
    const enquiryDataFromPayloadEnquiryIds =
      await this.enquiryRepository.aggregate([
        {
          $match: {
            _id: {
              $in: enquiryIdArr,
            },
          },
        },
      ]);
    if (enquiryDataFromPayloadEnquiryIds.length) {
      const isAnyRoundRobinAssignedIndex = enquiryData.findIndex(
        (enq) => enq.round_robin_assigned === RoundRobinAssignedStatus.YES,
      );
      this.loggerService.log(
        `Round robin assigned :: ${JSON.stringify(enquiryData[isAnyRoundRobinAssignedIndex])}`,
      );

      const maxEnquiry = enquiryDataFromPayloadEnquiryIds.length;
      let reMax = 0;
      if (isAnyRoundRobinAssignedIndex > -1) {
        reMax =
          employeeIds.findIndex(
            (e: number) =>
              e === enquiryData[isAnyRoundRobinAssignedIndex].assigned_to_id,
          ) + 1;
      }

      await Promise.all([
        ...(isAnyRoundRobinAssignedIndex > -1
          ? [
              this.enquiryRepository.updateById(
                enquiryData[isAnyRoundRobinAssignedIndex]._id,
                { round_robin_assigned: RoundRobinAssignedStatus.NO },
              ),
            ]
          : []),
        this.enquiryHelper.roundRobinAssign(
          enquiryDataFromPayloadEnquiryIds,
          reMax,
          maxEnquiry,
          response,
          schoolLocationDetails?.value,
        ),
        ...enquiryIds.map((enquiryId) => {
          return this.enquiryRepository.updateById(
            new Types.ObjectId(enquiryId),
            {
              school_location: {
                id: schoolLocationDetails.id,
                value: schoolLocationDetails.value,
              },
            },
          );
        }),
      ]);
    }

    // const transferedEnquiries = await Promise.all(promises);
    // const transferAuditLogs = [];
    // transferedEnquiries.map((enquiry) => {
    //   transferAuditLogs.push(
    //     this.auditLogRepository.create({
    //       table_name: 'enquiry',
    //       request_body: schoolLocationDetails,
    //       response_body: enquiry,
    //       operation_name: 'updateTransferEnquiry',
    //       created_by: 1,
    //       url: 'marketing/enquiry/transfer',
    //       ip_address: req.ip,
    //       method: HTTP_METHODS.PATCH,
    //       source_service: this.configService.get<string>('SERVICE'),
    //       record_id: enquiry._id,
    //     }),
    //   );
    // });

    // await Promise.all(transferAuditLogs);

    // below function sends notification
    // enquiryDataFromPayloadEnquiryIds.forEach((enquiry) => {
    //   this.emailService.setEnquiryDetails(enquiry).sendNotification(
    //     EMAIL_TEMPLATE_SLUGS.ENQUIRY_TRANSFERED,
    //     {
    //       enq_no: enquiry.enquiry_number,
    //       e_signature: '+91 6003000700',
    //       link: 'https://www.vibgyorhigh.com/'
    //     },
    //     [
    //       this.enquiryHelper.getEnquirerDetails(enquiry, 'email')
    //         ?.email as string,
    //     ],
    //   );
    // })
    return;
  }

  async getEnquiryReassignDetails(enquiryId: string) {
    const enquiryDetails = await this.enquiryRepository.aggregate([
      {
        $match: {
          _id: new Types.ObjectId(enquiryId),
        },
      },
      {
        $project: {
          _id: 1,
          parent_type: '$other_details.parent_type',
          parent_details: 1,
        },
      },
    ]);

    const { parent_details, parent_type, _id } = enquiryDetails[0];

    const similarEnquiries =
      await this.enquiryHelper.getSimilarEnquiriesByEnquirerGlobalId(
        parent_details,
        parent_type,
        _id,
      );

    delete enquiryDetails[0].parent_details;

    const result: any = { similarEnquiries: [] };
    if (similarEnquiries.length) {
      result.similarEnquiries = similarEnquiries.map((enquiry) => {
        return {
          enquiry_id: enquiry._id,
          enquiry_date: enquiry.enquiry_date,
          enquiry_number: enquiry.enquiry_number,
          enquiry_for: enquiry.other_details.enquiry_type,
          student_name:
            (enquiry?.student_details?.first_name ?? '') +
            ' ' +
            (enquiry?.student_details?.last_name ?? ''),
          stage_name: enquiry.stages
            ? enquiry.stages.find((stage) => stage.status === 'Open').stage_name
            : 'N/A',
          stage_status: enquiry.stages
            ? enquiry?.stages.find((stage) => stage.status === 'Open').status
            : 'N/A',
        };
      });
    }
    return result;
  }

  async reassign(
    enquiryIds: string[],
    reassignDetails: Record<string, any>,
    ipAddress: string,
  ) {
    const enquiryDoc = await this.enquiryRepository.getMany({
      _id: { $in: enquiryIds },
    });
    const promises = [];
    enquiryIds.forEach((enquiryId) => {
      promises.push(
        this.updateEnquiryData(enquiryId, {
          assigned_to: reassignDetails.assigned_to,
          assigned_to_id: reassignDetails.assigned_to_id,
        }),
      );
    });
    const reassignedEnquiries = await Promise.all(promises);

    const reassignAuditLogs = [];
    reassignedEnquiries.map((enquiry) => {
      const enquiryOld = enquiryDoc.find(
        (data) => data.enquiry_number === enquiry.enquiry_number,
      );
      reassignAuditLogs.push(
        this.auditLogRepository.create({
          table_name: 'enquiry',
          request_body: reassignDetails,
          response_body: reassignedEnquiries,
          operation_name: 'updateReassignEnquiry',
          created_by: reassignDetails.assigned_to_id,
          url: 'marketing/enquiry/reassign',
          ip_address: ipAddress,
          method: HTTP_METHODS.PATCH,
          source_service: this.configService.get<string>('SERVICE'),
          record_id: enquiry._id,
        }),
        this.enquiryLogService.createLog({
          enquiry_id: enquiry._id,
          event_type: EEnquiryEventType.REASSIGN,
          event_sub_type: EEnquiryEventSubType.ADMISSION_ACTION,
          event: EEnquiryEvent.ENQUIRY_REASSIGNED,
          created_by: `${enquiryOld.assigned_to}`,
          created_by_id: enquiryOld.assigned_to_id,
        }),
      );
    });

    await Promise.all(reassignAuditLogs);

    // const enqDetails = await this.enquiryRepository.getMany({
    //   _id: { $in: enquiryIds },
    // });

    // below function sends notification
    // enqDetails.forEach((enquiry) => {
    //   this.emailService.setEnquiryDetails(enquiry).sendNotification(
    //     EMAIL_TEMPLATE_SLUGS.ENQUIRY_REASSIGNED,
    //     {
    //       enq_no: enquiry.enquiry_number,
    //       e_signature: '+91 6003000700',
    //       link: 'https://www.vibgyorhigh.com/'
    //     },
    //     [
    //       this.enquiryHelper.getEnquirerDetails(enquiry, 'email')
    //         ?.email as string,
    //     ],
    //   );
    // })
  }

  async reassignEmployee(school_code: number, hris_code: string) {
    let roleCodes: string[] = [];

    const isSdoRoleCode = getSdoRoleCodes().includes(hris_code);
    const isCC = getCcReHrisCodes().CC.indexOf(hris_code) > -1 ? true : false;
    const isRE = getCcReHrisCodes().RE.indexOf(hris_code) > -1 ? true : false;

    if (isCC) {
      // If logged in user is CC, then get a list of CC employees of that school
      roleCodes = [...getCcReHrisCodes().CC];
    } else if (isRE || isSdoRoleCode) {
      // If logged in user is RE or SDO, then get a list of employess of RE and SDO role code employees of that school
      roleCodes = [...getCcReHrisCodes().RE, ...getSdoRoleCodes()];
    } else {
      // If logged in user is neither CC nor RE nor SDO then throw error
      throw new BadRequestException('No employee');
    }
    const filter: (string | number)[][] = roleCodes.map((data) => {
      return [
        'filters[$and][1][hr_hris_unique_role][HRIS_Unique_Role_Code][$in]',
        data,
      ];
    });

    // Get active and serving notice period employees
    [
      EMPLOYEE_ACTIVITY_STATUS.ACTIVE,
      EMPLOYEE_ACTIVITY_STATUS.SERVING_NOTICE_PERIOD,
    ].forEach((status, index) => {
      filter.push([`filters[Employment_Status][id][$in][${index}]`, status]);
    });

    const response = await this.mdmService.fetchDataFromAPI(
      `/api/hr-employee-masters`,
      [
        [
          'filters[$and][0][Base_Location][Base_Location][parent1_id][$eq]',
          school_code,
        ],
        ...filter,
      ],
    );

    let data = [];
    if (response.data) {
      data = response.data;
    }
    return data;
  }

  async globalSearchEnquiryListing(
    req: Request,
    pageNumber: number,
    pageSize: number,
    globalSearchText: string,
  ) {
    const result = await this.getEnquiryDetailsCC(
      req,
      pageNumber,
      pageSize,
      null,
      globalSearchText,
    );
    return result;
  }

  async checkIfEnquiryExists(
    enquiryId: string,
  ): Promise<Partial<EnquiryDocument & Document>> {
    const getEnquiryDetails = await this.enquiryRepository.getById(
      new Types.ObjectId(enquiryId),
    );
    if (!getEnquiryDetails) {
      throw new HttpException('Details not found', HttpStatus.NOT_FOUND);
    }
    return getEnquiryDetails;
  }

  async getMergeEnquiryDetails(
    enquiryId: string,
    body?: GetMergeDto | PostMergeDto,
  ): Promise<{
    enquirerDetails: Record<string, any>;
    similarEnquiries: Record<string, any>[];
  }> {
    const enquiryDetails = await this.checkIfEnquiryExists(enquiryId);

    const enquirerDetails: Record<string, unknown> = {};

    switch ((enquiryDetails.other_details as any).parent_type) {
      case EParentType.FATHER:
        const { father_details } = enquiryDetails.parent_details;
        enquirerDetails['name'] =
          father_details.first_name + father_details.last_name;
        enquirerDetails['email'] = father_details.email;
        enquirerDetails['mobile'] = father_details.mobile;
        break;
      case EParentType.MOTHER:
        const { mother_details } = enquiryDetails.parent_details;
        enquirerDetails['name'] =
          mother_details.first_name + mother_details.last_name;
        enquirerDetails['email'] = mother_details.email;
        enquirerDetails['mobile'] = mother_details.mobile;
        break;
      case EParentType.GUARDIAN:
        const { guardian_details } = enquiryDetails.parent_details;
        enquirerDetails['name'] =
          guardian_details.first_name + guardian_details.last_name;
        enquirerDetails['email'] = guardian_details.email;
        enquirerDetails['mobile'] = guardian_details.mobile;
        break;
    }

    let isEnquiryEligibleForMerge = true;
    if (
      !enquirerDetails['name'] ||
      !enquirerDetails['email'] ||
      !enquirerDetails['mobile'] ||
      !enquiryDetails?.student_details?.first_name ||
      !enquiryDetails.student_details.last_name ||
      !enquiryDetails.student_details?.dob ||
      enquiryDetails.status !== EEnquiryStatus.OPEN
    ) {
      isEnquiryEligibleForMerge = false;
    }

    if (!isEnquiryEligibleForMerge) {
      return { enquirerDetails, similarEnquiries: [] };
    }

    const { student_details } = enquiryDetails;

    const pipeline = [
      {
        $match: {
          $or: [
            {
              $or: [
                {
                  $or: [
                    {
                      'parent_details.father_details.mobile':
                        enquirerDetails['mobile'],
                    },
                    {
                      'parent_details.father_details.email':
                        enquirerDetails['email'],
                    },
                  ],
                },
                {
                  $or: [
                    {
                      'parent_details.mother_details.mobile':
                        enquirerDetails['mobile'],
                    },
                    {
                      'parent_details.mother_details.email':
                        enquirerDetails['email'],
                    },
                  ],
                },
                {
                  $or: [
                    {
                      'parent_details.guardian_details.mobile':
                        enquirerDetails['mobile'],
                    },
                    {
                      'parent_details.guardian_details.email':
                        enquirerDetails['email'],
                    },
                  ],
                },
              ],
            },
            {
              $and: [
                { 'student_details.first_name': student_details.first_name },
                { 'student_details.last_name': student_details.last_name },
                { 'student_details.dob': student_details.dob },
              ],
            },
          ],
          status: EEnquiryStatus.OPEN,
          _id: { $ne: new Types.ObjectId(enquiryId) },
        },
      },
    ];

    if (body?.user_id) {
      pipeline[0]['$match']['assigned_to_id'] = +body.user_id;
    }
    const similarEnquiries = await this.enquiryRepository.aggregate(pipeline);

    if (!similarEnquiries.length) {
      return { enquirerDetails, similarEnquiries };
    }

    const enquiriesToBeMerged = [];
    similarEnquiries.forEach((enquiry) => {
      enquiriesToBeMerged.push({
        enquiry_id: enquiry._id,
        enquiry_date: moment(enquiry.enquiry_date).format('DD-MM-YYYY'),
        enquiry_number: enquiry.enquiry_number,
        enquiry_for: enquiry.other_details.enquiry_type,
        student_name:
          (enquiry.student_details.first_name ?? '') +
          (enquiry.student_details.last_name ?? ''),
        school_name: enquiry.school_location.value, // Should we bring this name from MDM ?
        stage:
          enquiry?.enquiry_stages?.find(
            (stage) => stage?.status === EEnquiryStageStatus.OPEN,
          )?.stage_name ?? 'N/A',
        status: enquiry.status,
      });
    });

    return { enquirerDetails, similarEnquiries: enquiriesToBeMerged };
  }

  async mergeEnquiry(targetEnquiry: string, body: PostMergeDto): Promise<void> {
    const { enquiryIds } = body;

    const { similarEnquiries } = await this.getMergeEnquiryDetails(
      targetEnquiry,
      body,
    );

    if (!similarEnquiries.length && !enquiryIds.length) {
      throw new HttpException(
        'There are no valid enquiries to be merged with the target enquiry',
        HttpStatus.BAD_REQUEST,
      );
    }

    // check if enquiry ids given are matching under similar enquiries
    let isSourceEnquiryMergable = false;
    for (const enquiryId of enquiryIds) {
      const isSimilar = similarEnquiries.find(
        (enquiry) => enquiry.enquiry_id.toString() === enquiryId,
      );
      if (!isSimilar) {
        isSourceEnquiryMergable = false;
        break;
      }
      isSourceEnquiryMergable = true;
    }

    if (!isSourceEnquiryMergable) {
      throw new HttpException(
        'Source enquiry cannot be merged with the target enquiry',
        HttpStatus.BAD_REQUEST,
      );
    }
    const mongoIdEnquiry = enquiryIds.map((id) => new Types.ObjectId(id));

    const enquiryDetails: Partial<EnquiryDocument & Document>[] =
      await this.enquiryRepository.getManyWithId(
        {
          _id: {
            $in: [...mongoIdEnquiry, new Types.ObjectId(targetEnquiry)],
          },
        },
        {
          _id: 1,
          __v: 0,
          enquiry_form_id: 0,
          enquiry_type_id: 0,
          documents: 0,
          other_details: 0,
        },
      );

    // if (enquiryDetails.length !== 2) {
    //   throw new HttpException(
    //     'Enquiry details not found',
    //     HttpStatus.NOT_FOUND,
    //   );
    // }
    const target = enquiryDetails.find(
      (enquiry) => enquiry._id.toString() === targetEnquiry,
    );
    const targetEnquiryId: Types.ObjectId = target._id;
    delete target._id;

    const sourceEnquiryIds: Types.ObjectId[] = [];
    for (const sourceEnquiry of enquiryIds) {
      const source = enquiryDetails.find(
        (enquiry) => enquiry._id.toString() === sourceEnquiry,
      );
      sourceEnquiryIds.push(source._id);
      delete source._id;
    }
    const targetEnquiryDoc =
      await this.enquiryRepository.getById(targetEnquiryId);
    // const updateResult = await this.enquiryRepository.updateOne(
    //   { _id: targetEnquiryId },
    //   {
    //     parent_enquiry_number: targetEnquiryDoc.enquiry_number,
    //   },
    // );

    // if (!updateResult.modifiedCount) {
    //   throw new HttpException(
    //     'Something went wrong while merging',
    //     HttpStatus.INTERNAL_SERVER_ERROR,
    //   );
    // }
    for (const sourceEnquiryId of sourceEnquiryIds) {
      await Promise.all([
        this.enquiryRepository.updateById(sourceEnquiryId, {
          status: EEnquiryStatus.CLOSED,
          parent_enquiry_number: targetEnquiryDoc.enquiry_number,
        }),
        this.enquiryLogService.createLog({
          enquiry_id: sourceEnquiryId,
          event_type: EEnquiryEventType.ENQUIRY,
          event_sub_type: EEnquiryEventSubType.ENQUIRY_ACTION,
          event: `${EEnquiryEvent.ENQUIRY_MERGED} ${targetEnquiryDoc._id}`,
          created_by: 'user',
          log_data: {
            sourceEnquiryId: sourceEnquiryId,
            targetEnquiryId: targetEnquiryId,
            message: 'Enquiry merged',
          },
          created_by_id: 1,
        }),
        this.enquiryLogService.createLog({
          enquiry_id: sourceEnquiryId,
          event_type: EEnquiryEventType.ENQUIRY,
          event_sub_type: EEnquiryEventSubType.ENQUIRY_ACTION,
          event: EEnquiryEvent.ENQUIRY_CLOSED,
          log_data: {
            sourceEnquiryId: sourceEnquiryId,
            targetEnquiryId: targetEnquiryId,
            message: 'Enquiry closed by merging',
          },
          created_by: 'user',
          created_by_id: 1,
        }),
      ]);
    }
    return;
  }

  async deleteUploadedDocument(
    enquiryId: string,
    documentId: number,
  ): Promise<void> {
    const enquiry = await this.enquiryRepository.getById(
      new Types.ObjectId(enquiryId),
    );
    if (!enquiry) {
      throw new HttpException('Enquiry not found', HttpStatus.NOT_FOUND);
    }

    const { documents } = enquiry;

    const updatedDocuments = documents.map((document) => {
      if (document.document_id === documentId) {
        document.file = null;
        document.is_deleted = true;
        document.is_verified = false;
      }
      return document;
    });

    await this.enquiryRepository.updateById(new Types.ObjectId(enquiryId), {
      documents: updatedDocuments,
    });
    return;
  }

  async verifyUploadedDocument(
    enquiryId: string,
    documentId: number,
    verify: boolean,
  ): Promise<void> {
    const enquiry = await this.enquiryRepository.getById(
      new Types.ObjectId(enquiryId),
    );
    if (!enquiry) {
      throw new HttpException('Enquiry not found', HttpStatus.NOT_FOUND);
    }

    const { documents } = enquiry;

    const document = documents.find(
      (document) => document.document_id === documentId,
    );

    if (document.is_deleted) {
      throw new HttpException(
        'Cannot verify or unverify a deleted document',
        HttpStatus.BAD_REQUEST,
      );
    }

    if (!document.file) {
      throw new HttpException(
        'Cannot verify or unverify as file not found',
        HttpStatus.BAD_REQUEST,
      );
    }

    const updatedDocuments = documents.map((document) => {
      if (document.document_id === documentId) {
        document.is_verified = verify;
      }
      return document;
    });

    await this.enquiryRepository.updateById(new Types.ObjectId(enquiryId), {
      documents: updatedDocuments,
    });
    return;
  }

  async getEnquiryDetailsForFinance(req: Request, enquiryId: string) {
    const enquiryDetails = await this.enquiryRepository.getById(
      new Types.ObjectId(enquiryId),
    );
    if (!enquiryDetails) {
      throw new HttpException(
        'Enquiry details not found',
        HttpStatus.NOT_FOUND,
      );
    }

    const schoolDoc = await this.mdmService.fetchDataFromAPI(
      `${MDM_API_URLS.SCHOOL}/${enquiryDetails.school_location.id}`,
    );

    const {
      student_details,
      academic_year,
      enquiry_number,
      school_location,
      other_details,
      brand,
      board,
    } = enquiryDetails;

    const academicYearDetails = await this.mdmService.fetchDataFromAPI(
      `${MDM_API_URLS.ACADEMIC_YEAR}/${academic_year?.id}`,
    );

    const searchSchoolResponse = await this.axiosService
      .setBaseUrl(this.configService.get<string>('ADMIN_PANEL_URL'))
      .setUrl(ADMIN_PANEL_URL.SEARCH_SCHOOL_LIST)
      .setMethod(EHttpCallMethods.POST)
      .setHeaders({
        Authorization: req.headers.authorization,
      } as AxiosRequestHeaders)
      .setBody({
        academic_year_id: [
          +academicYearDetails?.data?.attributes?.short_name_two_digit,
        ],
        school_id: [school_location?.id],
        brand_id: [brand?.id],
        board_id: [board?.id],
        grade_id: [student_details?.grade?.id],
        pageSize: 1,
        page: 1,
      })
      .sendRequest();
    const enquiryResponse = new Object();
    enquiryResponse['enquiry_id'] = enquiryId;
    enquiryResponse['student_name'] =
      (student_details?.first_name ?? '') +
      ' ' +
      (student_details?.last_name ?? '');
    enquiryResponse['enquiry_number'] = enquiry_number ? enquiry_number : null;
    enquiryResponse['school'] = school_location?.value
      ? school_location.value
      : null;
    enquiryResponse['school_id'] = school_location?.id
      ? school_location.id
      : null;
    enquiryResponse['stream'] = (enquiryDetails as any)?.stream?.value
      ? (enquiryDetails as any)?.stream?.value
      : null;
    enquiryResponse['stream_id'] = (enquiryDetails as any)?.stream?.id
      ? (enquiryDetails as any)?.stream?.id
      : null;
    enquiryResponse['brand'] = (enquiryDetails as any)?.brand?.value
      ? (enquiryDetails as any)?.brand?.value
      : null;
    enquiryResponse['brand_id'] = (enquiryDetails as any)?.brand?.id
      ? (enquiryDetails as any)?.brand?.id
      : null;
    enquiryResponse['board'] = (enquiryDetails as any)?.board?.value
      ? (enquiryDetails as any)?.board?.value
      : null;
    enquiryResponse['board_id'] = (enquiryDetails as any)?.board?.id
      ? (enquiryDetails as any)?.board?.id
      : null;
    enquiryResponse['course'] = (enquiryDetails as any)?.course?.value
      ? (enquiryDetails as any)?.course?.value
      : null;
    enquiryResponse['course_id'] = (enquiryDetails as any)?.course?.id
      ? (enquiryDetails as any)?.course?.id
      : null;
    enquiryResponse['grade'] = student_details?.grade?.value
      ? student_details?.grade?.value
      : null;
    enquiryResponse['grade_id'] = student_details?.grade?.id
      ? student_details?.grade?.id
      : null;
    enquiryResponse['shift'] = (enquiryDetails as any)?.shift?.value
      ? (enquiryDetails as any)?.shift?.value
      : null;
    enquiryResponse['shift_id'] = (enquiryDetails as any)?.shift?.id
      ? (enquiryDetails as any)?.shift?.id
      : null;
    enquiryResponse['academic_year'] = academic_year?.value
      ? academic_year.value
      : null;
    enquiryResponse['academic_year_id'] = academic_year?.id
      ? academic_year.id
      : null;
    enquiryResponse['concession_tags'] = other_details?.student_slug
      ? other_details?.student_slug
      : null;

    enquiryResponse['school_parent_id'] = schoolDoc?.data?.attributes
      ?.school_parent_id
      ? schoolDoc?.data?.attributes?.school_parent_id
      : null;

    enquiryResponse['lob_id'] =
      searchSchoolResponse?.data?.data?.data[0]?.lob_id;
    switch ((enquiryDetails.other_details as any).parent_type) {
      case EParentType.FATHER:
        const { father_details } = enquiryDetails.parent_details;
        enquiryResponse['guardian_name'] =
          father_details.first_name + ' ' + father_details.last_name;
        enquiryResponse['guardian_mobile'] = father_details.mobile;
        break;
      case EParentType.MOTHER:
        const { mother_details } = enquiryDetails.parent_details;
        enquiryResponse['guardian_name'] =
          mother_details.first_name + ' ' + mother_details.last_name;
        enquiryResponse['guardian_mobile'] = mother_details.mobile;
        break;
      case EParentType.GUARDIAN:
        const { guardian_details } = enquiryDetails.parent_details;
        enquiryResponse['guardian_name'] =
          guardian_details.first_name + ' ' + guardian_details.last_name;
        enquiryResponse['guardian_mobile'] = guardian_details.mobile;
        break;
    }

    return enquiryResponse;
  }

  async searchEnquiriesForFinance(searchPayload: {
    search: string;
    school_id: number[];
  }) {
    const { search, school_id } = searchPayload;
    const pipeline: PipelineStage[] = [
      {
        $match: {
          $or: [
            buildFilter('enquiry_number', 'contains', search),
            buildFilter('student_details.first_name', 'contains', search),
            buildFilter('student_details.last_name', 'contains', search),
            buildFilter(
              'parent_details.father_details.mobile',
              'contains',
              search,
            ),
            buildFilter(
              'parent_details.mother_details.mobile',
              'contains',
              search,
            ),
            buildFilter(
              'parent_details.guardian_details.mobile',
              'contains',
              search,
            ),
          ],
          ...(school_id.length
            ? {
                'school_location.id': {
                  $in: school_id,
                },
              }
            : {}),
        },
      },
      {
        $lookup: {
          from: 'admission',
          localField: '_id',
          foreignField: 'enquiry_id',
          as: 'admissionDetails',
        },
      },
      {
        $addFields: {
          enquiryNumber: {
            $ifNull: ['$enquiry_number', null],
          },
        },
      },
      {
        $match: {
          $or: [
            {
              admissionDetails: { $size: 0 },
            },
            {
              $and: [
                {
                  admissionDetails: { $size: 1 },
                },
                {
                  'admissionDetails.student_id': { $eq: null },
                },
              ],
            },
          ],
        },
      },
      {
        $project: {
          _id: 0,
          enquiry_id: '$_id',
          enquiry_number: '$enquiryNumber',
          student_first_name: '$student_details.first_name',
          student_last_name: '$student_details.last_name',
        },
      },
    ];

    console.log('Pipeline ---> ', JSON.stringify(pipeline));

    const enquiryList = await this.enquiryRepository.aggregate(pipeline);
    return enquiryList?.length
      ? enquiryList.map((enquiry) => {
          return {
            id: enquiry.enquiry_id,
            display_name:
              (enquiry?.student_first_name ?? '') +
              ' ' +
              (enquiry?.student_last_name ?? '') +
              ' - ' +
              enquiry.enquiry_number,
            enr_no: enquiry.enquiry_number,
          };
        })
      : [];
  }

  async calculateEligibleGrade(
    academicYearId: number,
    schoolId: number,
    dob: string,
  ) {
    const [academicYearDetails, schoolDetails] = await Promise.all([
      this.mdmService.fetchDataFromAPI(
        `${MDM_API_URLS.ACADEMIC_YEAR}/${academicYearId}`,
      ),
      this.mdmService.fetchDataFromAPI(`${MDM_API_URLS.SCHOOL}/${schoolId}`),
    ]);

    const academicYear = academicYearDetails?.data?.attributes?.short_name;
    const schoolStateId = schoolDetails?.data?.attributes?.state_id;

    if (!academicYear) {
      throw new HttpException(
        'Incorrect academic year ID',
        HttpStatus.BAD_REQUEST,
      );
    }

    if (!schoolStateId) {
      throw new HttpException('Incorrect school ID', HttpStatus.BAD_REQUEST);
    }

    const stateDetails = await this.mdmService.fetchDataFromAPI(
      `${MDM_API_URLS.STATE}/${schoolStateId}`,
    );

    const state = stateDetails?.data?.attributes?.name;

    if (!state) {
      throw new HttpException('State detail not found', HttpStatus.BAD_REQUEST);
    }

    const mapping = STATE_AGE_MAPPING.find((mapping) => {
      return mapping.state.toLowerCase() === state.toLowerCase();
    });

    if (!mapping) {
      return { eligibleGrade: 'N/A' };
    }

    const endDate =
      '20' + academicYear.split('-')[0] + '-' + mapping.eligibleAsOf;

    const { years, months, days } = this.enquiryHelper.calculateAge(
      dob,
      endDate,
    );

    let eligibleGrade = 'N/A';

    for (let i = 0; i < mapping.gradeAgeMapping.length; i++) {
      const {
        years: eligibleYear,
        months: eligibleMonth,
        days: eligibleDays,
      } = mapping.gradeAgeMapping[i].age;
      if (years === eligibleYear) {
        if (months === eligibleMonth) {
          if (days >= eligibleDays) {
            eligibleGrade = mapping.gradeAgeMapping[i].grade;
            break;
          } else {
            eligibleGrade = mapping.gradeAgeMapping[i - 1].grade;
            break;
          }
        } else if (months > eligibleMonth) {
          eligibleGrade = mapping.gradeAgeMapping[i].grade;
          break;
        } else {
          eligibleGrade = mapping.gradeAgeMapping[i + 1].grade;
          break;
        }
      }
    }
    return { eligibleGrade };
  }

  async updatePaymentData(paymentData: Record<string, any>, req: Request) {
    const {
      enquiry_id,
      payment_type,
      enquiry_number,
      amount,
      mode_of_payment,
      payment_date_time,
    } = paymentData;

    const enquiryDetails = await this.enquiryRepository.getOne({
      _id: new Types.ObjectId(enquiry_id as string),
      enquiry_number: enquiry_number,
    });

    if (!enquiryDetails) {
      throw new HttpException(
        'Enquiry details not found',
        HttpStatus.NOT_FOUND,
      );
    }

    if (payment_type === EPaymentType.REGISTRATION) {
      const tPlusFiveDate = new Date();
      tPlusFiveDate.setDate(new Date().getDate() + 5);
      tPlusFiveDate.setHours(23, 59, 59, 999);

      await Promise.all([
        this.enquiryRepository.updateById(
          new Types.ObjectId(enquiry_id as string),
          {
            registration_fees_paid: true,
            registration_payment_details: {
              amount: amount,
              mode_of_payment: mode_of_payment,
              payment_date_time: payment_date_time,
            },
          },
        ),
        this.enquiryLogService.createLog({
          enquiry_id: enquiryDetails._id,
          event_type: EEnquiryEventType.REGISTRATION,
          event_sub_type: EEnquiryEventSubType.REGISTRATION_ACTION,
          event: EEnquiryEvent.REGISTRATION_FEE_RECEIVED,
          log_data: {
            registration_payment_details: {
              amount: amount,
              mode_of_payment: mode_of_payment,
              payment_date_time: payment_date_time,
            },
          },
          created_by: 'System',
          created_by_id: 1,
        }),
        // TODO: This call must be removed after implementing kit number logic in production
        process.env.NODE_ENV === 'production'
          ? this.enquiryStageUpdateService.moveToNextStage(
              enquiry_id,
              'Academic Kit Selling',
              req,
            )
          : () => Promise.resolve(true),
      ]);
      const { school_location } = enquiryDetails;
      //TODO: Make the below block of code non env specific once the kit number logic is implemented in production
      if (process.env.NODE_ENV !== 'production') {
        const kitNumberResponse = await this.mdmService.postDataToAPI(
          MDM_API_URLS.GENERATE_KIT_NUMBER,
          { school_id: school_location.id },
        );
        const kitNumber = (kitNumberResponse as any)?.data?.number;
        await this.enquiryRepository.updateById(
          new Types.ObjectId(enquiry_id),
          { generated_kit_number: kitNumber },
        );
      }
      return true;
    } else if (
      payment_type === EPaymentType.ADMISSION ||
      payment_type === EPaymentType.CONSOLIDATED ||
      payment_type === EPaymentType.PSA ||
      payment_type === EPaymentType.KIDS_CLUB
    ) {
      const paymentDetails = {
        enrollment_number: paymentData?.enrollment_number ?? null,
        gr_number: paymentData?.gr_number ?? null,
        amount: amount,
        mode_of_payment: mode_of_payment,
        payment_date_time: payment_date_time,
      };

      const tPlusFiveDate = new Date();
      tPlusFiveDate.setDate(new Date().getDate() + 5);
      tPlusFiveDate.setHours(23, 59, 59, 999);

      const isAdmissionFeeReceivedLog = await this.enquiryLogRepository.getOne({
        enquiry_id: enquiryDetails._id,
        event: EEnquiryEvent.ADMISSION_FEE_RECEIVED,
      });

      if (isAdmissionFeeReceivedLog) {
        this.loggerService.log(`Admission fee receive log already exists !!`);
        this.loggerService.log(`Not proceeding to insert student details !!`);
        return true;
      }

      try {
        // Purposely placing the create log outside of Promise.all to handle the race condition
        await this.enquiryLogService.createLog({
          enquiry_id: enquiryDetails._id,
          event_type: EEnquiryEventType.ADMISSION,
          event_sub_type: EEnquiryEventSubType.ADMISSION_ACTION,
          event: EEnquiryEvent.ADMISSION_FEE_RECEIVED,
          log_data: {
            admission_payment_details: paymentDetails,
          },
          created_by: 'System',
          created_by_id: 1,
        });

        await Promise.all([
          this.admissionService.updateAdmissionPaymentStatus(
            enquiry_id as string,
            paymentDetails,
          ),
          this.enquiryStageUpdateService.moveToNextStage(
            enquiry_id,
            'Payment',
            req,
          ),
          this.myTaskService.createMyTask({
            enquiry_id: enquiry_id,
            created_for_stage: ETaskEntityType.ADMITTED_OR_PROVISIONAL_APPROVAL,
            task_creation_count: 1,
            valid_from: new Date(),
            valid_till: tPlusFiveDate,
            assigned_to_id: enquiryDetails.assigned_to_id,
          }),
        ]);
        return true;
      } catch (err) {
        this.loggerService.log(
          `Error occured while processing the admission fee !!`,
        );
        // await this.enquiryLogRepository.hardDeleteById(
        //   isAdmissionFeeReceivedLog._id,
        // );
        throw err;
      }
    }
    return false;
  }

  async updateEnquiryStatus(
    enquiryId: string,
    status: EEnquiryStatus,
    metadata: Record<string, any> = null,
  ): Promise<void> {
    switch (status) {
      case EEnquiryStatus.CLOSED:
        const enquiry = await this.enquiryRepository.getById(
          new Types.ObjectId(enquiryId),
        );
        await Promise.all([
          this.enquiryRepository.updateById(new Types.ObjectId(enquiryId), {
            status: EEnquiryStatus.CLOSED,
          }),
          await this.enquiryLogService.createLog({
            enquiry_id: new Types.ObjectId(enquiryId),
            event_type: EEnquiryEventType.ENQUIRY,
            event_sub_type: EEnquiryEventSubType.ENQUIRY_ACTION,
            event: EEnquiryEvent.ENQUIRY_CLOSED,
            log_data: metadata,
            created_by: 'User',
            created_by_id: 1,
          }),
        ]);

        // below function sends notification
        // this.emailService.setEnquiryDetails(enquiry).sendNotification(
        //   EMAIL_TEMPLATE_SLUGS.ENQUIRY_CLOSED,
        //   {
        //     enq_no: enquiry.enquiry_number,
        //     e_signature: '+91 6003000700',
        //     link: 'https://www.vibgyorhigh.com/'
        //   },
        //   [
        //     this.enquiryHelper.getEnquirerDetails(enquiry, 'email')
        //       ?.email as string,
        //   ],
        // );

        break;
      case EEnquiryStatus.ON_HOLD:
        break;
      case EEnquiryStatus.OPEN:
        break;
    }
    return;
  }

  async triggerBulkDetailApi(
    workflow_ids: string,
    token: string,
    crossPlatformRequest: boolean,
  ) {
    return this.axiosService
      .setBaseUrl(`${this.configService.get<string>('ADMIN_PANEL_URL')}`)
      .setMethod(EHttpCallMethods.POST)
      .setHeaders({ Authorization: token } as AxiosRequestHeaders)
      .setUrl(ADMIN_PANEL_URL.GET_WORKFLOW_BULK_DEATIL)
      .setBody({ workflowIds: [workflow_ids] })
      .isCrossPlatformRequest(crossPlatformRequest)
      .sendRequest();
  }

  async updateAdmissionStatus(admisionDto: UpdateAdmissionDto, req: Request) {
    const isAppReq = isAppRequest(req);
    const workflowLogDoc = await this.triggerBulkDetailApi(
      admisionDto.workflow_id,
      req.headers.authorization,
      isAppReq,
    );
    if (!workflowLogDoc?.data?.data) {
      throw new NotFoundException(
        `logs not found for workflow id :: ${admisionDto.workflow_id}`,
      );
    }
    const [data] = workflowLogDoc.data.data;

    const enquiryId = new Types.ObjectId(data.module_id as string);
    const enquiryDetails = await this.enquiryRepository.getById(enquiryId);
    if (enquiryDetails?.other_details?.enquiry_type === EEnquiryType.IVT) {
    }
    const status = AdmissionStatus[admisionDto.status];
    const stage_name = 'Admission Status';

    const result = await this.enquiryRepository.updateOne(
      { _id: enquiryId },
      { $set: { 'enquiry_stages.$[elem].status': status } },
      {
        arrayFilters: [{ 'elem.stage_name': stage_name }],
        new: true,
      },
    );

    await Promise.all([
      this.admissionRepository.updateByEnquiryId(enquiryId, {
        admission_approval_status:
          EAdmissionApprovalStatus[AdmissionStatus[admisionDto.status]],
      }),
      ...(enquiryDetails?.other_details?.enquiry_type === EEnquiryType.IVT ||
      enquiryDetails?.other_details?.enquiry_type === EEnquiryType.READMISSION
        ? [
            this.admissionRepository.create({
              enquiry_id: enquiryId,
              admission_approval_status: status,
            }),
          ]
        : []),
      this.enquiryLogService.createLog({
        enquiry_id: enquiryId,
        event_type: EEnquiryEventType.ADMISSION,
        event_sub_type: EEnquiryEventSubType.ADMISSION_ACTION,
        event:
          status === EAdmissionApprovalStatus.APPROVED
            ? EEnquiryEvent.ADMISSION_APPROVED
            : EEnquiryEvent.ADMISSION_REJECTED,
        log_data: {
          enquiry_id: enquiryId,
          admission_approval_status: status,
        },
        created_by: 'System',
        created_by_id: 1,
      }),
    ]);
    return result;
  }
  async generateTermsAndConditionPdf(
    enquiryId: string,
    schoolId: number,
    download: boolean = false,
  ) {
    const bucketName = this.configService.get<string>('BUCKET_NAME');
    const enquiryDoc = await this.enquiryRepository.getById(
      new Types.ObjectId(enquiryId),
    );
    if (!enquiryDoc) {
      throw new NotFoundException('Enquiry not found!');
    }
    const schoolResponse = await this.mdmService.fetchDataFromAPI(
      `${MDM_API_URLS.SCHOOL}/${schoolId}`,
    );
    if (!schoolResponse?.data?.attributes?.term_and_conditions) {
      throw new NotFoundException('terms and Condition is not there');
    }
    const template = applyTemplate(
      schoolResponse.data.attributes.term_and_conditions,
      {
        student_name: `${enquiryDoc?.student_details?.first_name} ${enquiryDoc?.student_details?.last_name}`,
        school_name: `${enquiryDoc?.school_location?.value}`,
      },
    );

    const buffer = await this.pdfService.createPdf(template);

    const filenamefile: Express.Multer.File =
      await this.fileService.createFileFromBuffer(
        buffer,
        'termsAndCondition.pdf',
        'application/pdf',
      );
    await this.setFileUploadStorage();
    const response = await this.storageService.uploadFile(filenamefile);
    const signedUrl = await this.storageService.getSignedUrl(
      bucketName,
      response,
      download,
    );
    return { url: signedUrl };
  }

  async acceptTermsAndCondition(enquiryId: string) {
    const enquiryDoc = await this.enquiryRepository.getById(
      new Types.ObjectId(enquiryId),
    );
    if (!enquiryDoc) {
      throw new NotFoundException('Enquiry not found!');
    }
    await this.enquiryRepository.updateById(new Types.ObjectId(enquiryId), {
      'other_details.are_terms_and_condition_accepted': true,
    });
    return;
  }

  //TODO: Remove this wrapper in future. This wrapper is a temporary solution for resolving the circular dependency error
  async moveToNextStageWrapper(
    enquiryId: string,
    currentStage: string,
    req: Request,
  ) {
    return await this.enquiryStageUpdateService.moveToNextStage(
      enquiryId,
      currentStage,
      req,
    );
  }

  // report export To Csv
  async enquiryDetailsReport(academicYearId: number) {
    const pipeline = [
      {
        $match: { 'academic_year.id': academicYearId },
      },
      {
        $sort: { created_at: -1 },
      },
      {
        $lookup: {
          from: 'enquiryType',
          localField: 'enquiry_type_id',
          foreignField: '_id',
          as: 'enquiry_type_details',
          pipeline: [
            {
              $project: {
                name: 1,
              },
            },
          ],
        },
      },
      {
        $unwind: {
          path: '$enquiry_type_details',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: 'admission',
          localField: '_id',
          foreignField: 'enquiry_id',
          as: 'admissionDetails',
        },
      },
      {
        $lookup: {
          from: 'enquiryStage',
          localField: 'enquiry_stages.stage_id',
          foreignField: '_id',
          as: 'stages',
        },
      },
      {
        $lookup: {
          from: 'followUps',
          localField: '_id',
          foreignField: 'enquiry_id',
          as: 'lastFollowUps',
          pipeline: [
            {
              $sort: {
                _id: -1,
              },
            },
          ],
        },
      },
      {
        $lookup: {
          from: 'enquiryLogs',
          localField: '_id',
          foreignField: 'enquiry_id',
          as: 'enquiryLogs',
          pipeline: [
            {
              $match: {
                event: {
                  $in: [
                    EEnquiryEvent.REGISTRATION_FEE_RECEIVED,
                    EEnquiryEvent.SCHOOL_TOUR_SCHEDULED,
                    EEnquiryEvent.SCHOOL_TOUR_RESCHEDULE,
                    EEnquiryEvent.SCHOOL_TOUR_COMPLETED,
                    EEnquiryEvent.REGISTRATION_FEE_RECEIVED,
                    EEnquiryEvent.ENQUIRY_CLOSED,
                  ],
                },
              },
            },
            {
              $sort: {
                _id: -1,
              },
            },
          ],
        },
      },
      // {
      //   $addFields: {
      //     completedStages: {
      //       $filter: {
      //         input: '$enquiry_stages',
      //         as: 'stage',
      //         cond: {
      //           $or: [
      //             { $eq: ['$$stage.status', EEnquiryStageStatus.COMPLETED] },
      //             { $eq: ['$$stage.status', EEnquiryStageStatus.PASSED] },
      //           ],
      //         },
      //       },
      //     },
      //   },
      // },
      // {
      //   $addFields: {
      //     lastCompletedStage: {
      //       $arrayElemAt: [
      //         '$completedStages',
      //         { $subtract: [{ $size: '$completedStages' }, 1] },
      //       ],
      //     },
      //   },
      // },
      // {
      //   $addFields: {
      //     lastCompletedStageIndex: {
      //       $indexOfArray: [
      //         '$enquiry_stages.stage_name',
      //         '$lastCompletedStage.stage_name',
      //       ],
      //     },
      //   },
      // },
      // {
      //   $addFields: {
      //     nextStage: {
      //       $cond: {
      //         if: {
      //           $eq: ['$lastCompletedStageIndex', 7],
      //         },
      //         then: {
      //           $arrayElemAt: ['$enquiry_stages', '$lastCompletedStageIndex'],
      //         },
      //         else: {
      //           $arrayElemAt: [
      //             '$enquiry_stages',
      //             {
      //               $add: ['$lastCompletedStageIndex', 1],
      //             },
      //           ],
      //         },
      //       },
      //     },
      //   },
      // },
      {
        $project: {
          _id: 1,
          school_name: '$school_location.value',
          school_id: '$school_location.id',
          enquiry_number: '$enquiry_number',
          enquiry_date: '$enquiry_date',
          student_first_name: '$student_details.first_name',
          student_last_name: '$student_details.last_name',
          enquiry_stages: 1,
          enquiry_status: {
            $cond: {
              if: {
                $eq: ['$status', EEnquiryStatus.ADMITTED],
              },
              then: EEnquiryStatus.CLOSED,
              else: '$status',
            },
          },
          // current_stage: {
          //   $cond: {
          //     if: {
          //       $eq: ['$lastCompletedStageIndex', 4],
          //     },
          //     then: {
          //       $cond: {
          //         if: {
          //           $eq: ['$nextStage.status', EEnquiryStageStatus.REJECTED],
          //         },
          //         then: 'Admission Cancelled',
          //         else: {
          //           $cond: {
          //             if: {
          //               $eq: [
          //                 '$nextStage.status',
          //                 EEnquiryStageStatus.APPROVED,
          //               ],
          //             },
          //             then: 'Admission Approved',
          //             else: '$nextStage.stage_name',
          //           },
          //         },
          //       },
          //     },
          //     else: '$nextStage.stage_name',
          //   },
          // },
          board: '$board.value',
          grade: '$student_details.grade.value',
          course: '$course.value',
          stream: '$stream.value',
          shift: '$shift.value',
          created_by: '$created_by.user_name',
          current_owner: '$assigned_to',
          enquiry_type: '$enquiry_type_details.name',
          enquiry_category: '$enquiry_category.value',
          academic_year: '$academic_year.value',
          sibling_details: '$sibling_details',
          enquiry_mode: '$enquiry_mode.value',
          enquiry_source_type: '$enquiry_source_type.value',
          enquiry_source: '$enquiry_source.value',
          enquiry_sub_source: '$enquiry_sub_source.value',
          enquirer_sso_details: {
            $switch: {
              branches: [
                {
                  case: {
                    $eq: ['$other_details.parent_type', EParentType.FATHER],
                  },
                  then: {
                    username: {
                      $ifNull: [
                        '$parent_details.father_details.sso_username',
                        null,
                      ],
                    },
                    password: {
                      $ifNull: [
                        '$parent_details.father_details.sso_password',
                        null,
                      ],
                    },
                  },
                },
                {
                  case: {
                    $eq: ['$other_details.parent_type', EParentType.MOTHER],
                  },
                  then: {
                    username: {
                      $ifNull: [
                        '$parent_details.mother_details.sso_username',
                        null,
                      ],
                    },
                    password: {
                      $ifNull: [
                        '$parent_details.mother_details.sso_password',
                        null,
                      ],
                    },
                  },
                },
                {
                  case: {
                    $eq: ['$other_details.parent_type', EParentType.GUARDIAN],
                  },
                  then: {
                    username: {
                      $ifNull: [
                        '$parent_details.guardian_details.sso_username',
                        null,
                      ],
                    },
                    password: {
                      $ifNull: [
                        '$parent_details.guardian_details.sso_password',
                        null,
                      ],
                    },
                  },
                },
              ],
              default: null,
            },
          },
          corporate_tie_up: {
            $cond: {
              if: {
                $eq: ['$enquiry_sub_source.value', 'Corporate Tie-Up'],
              },
              then: 'Yes',
              else: 'No',
            },
          },
          pre_school_tie_up: {
            $cond: {
              if: {
                $eq: ['$enquiry_sub_source.value', 'Pre School Tie-Up'],
              },
              then: 'Yes',
              else: 'No',
            },
          },
          parent_referral: {
            $cond: {
              if: {
                $eq: ['$enquiry_sub_source.value', 'Parent Referral'],
              },
              then: 'Yes',
              else: 'No',
            },
          },
          employee_referral: {
            $cond: {
              if: {
                $eq: ['$enquiry_sub_source.value', 'Employee Referral'],
              },
              then: 'Yes',
              else: 'No',
            },
          },
          school_location: '$school_location.value',
          parent_type: '$other_details.parent_type',
          created_at: '$created_at',
          admission_date: {
            $cond: {
              if: {
                $ifNull: ['$cancelled_admission_date', false],
              },
              then: {
                $dateToString: {
                  format: '%d-%m-%Y',
                  date: '$cancelled_admission_date',
                },
              },
              else: {
                $cond: {
                  if: {
                    $gt: [{ $size: '$admissionDetails' }, 0],
                  },
                  then: {
                    $dateToString: {
                      format: '%d-%m-%Y',
                      date: {
                        $arrayElemAt: ['$admissionDetails.admitted_at', 0],
                      },
                    },
                  },
                  else: null,
                },
              },
            },
          },
          next_follow_up_date: '$next_follow_up_at',
          next_follow_up: {
            $cond: {
              if: {
                $and: [
                  { $eq: ['$enquiry_mode.value', 'Digital (website)'] },
                  { $eq: [{ $size: '$lastFollowUps' }, 0] },
                ],
              },
              then: {
                $dateToString: {
                  format: '%d-%m-%Y',
                  date: '$created_at',
                },
              },
              else: {
                $cond: {
                  if: {
                    $and: [
                      { $gt: [{ $size: '$lastFollowUps' }, 0] },
                      {
                        $ne: [
                          { $arrayElemAt: ['$lastFollowUps.date', 0] },
                          null,
                        ],
                      },
                    ],
                  },
                  then: {
                    $dateToString: {
                      format: '%d-%m-%Y',
                      date: {
                        $dateFromString: {
                          dateString: {
                            $arrayElemAt: ['$lastFollowUps.date', 0],
                          },
                        },
                      },
                    },
                  },
                  else: null,
                },
              },
            },
          },
          query: {
            $ifNull: ['$other_details.query', null],
          },
          last_follow_up_date: {
            $cond: {
              if: { $gt: [{ $size: '$lastFollowUps' }, 0] },
              then: {
                $dateToString: {
                  format: '%d-%m-%Y',
                  date: { $arrayElemAt: ['$lastFollowUps.created_at', 0] },
                },
              },
              else: null,
            },
          },
          last_follow_up_remark: {
            $cond: {
              if: { $gt: [{ $size: '$lastFollowUps' }, 0] },
              then: { $arrayElemAt: ['$lastFollowUps.remarks', 0] },
              else: null,
            },
          },
          school_tour_scheduled_date: {
            $cond: {
              if: {
                $gt: [{ $size: '$enquiryLogs' }, 0],
              },
              then: {
                $let: {
                  vars: {
                    scheduledSchoolTourLog: {
                      $filter: {
                        input: '$enquiryLogs',
                        as: 'log',
                        cond: {
                          $in: [
                            '$$log.event',
                            [
                              EEnquiryEvent.SCHOOL_TOUR_SCHEDULED,
                              EEnquiryEvent.SCHOOL_TOUR_RESCHEDULE,
                            ],
                          ],
                        },
                      },
                    },
                  },
                  in: {
                    $ifNull: [
                      {
                        $arrayElemAt: [
                          '$$scheduledSchoolTourLog.log_data.date',
                          0,
                        ],
                      },
                      null,
                    ],
                  },
                },
              },
              else: null,
            },
          },
          walkin_date: {
            $let: {
              vars: {
                schoolTourLogs: {
                  $filter: {
                    input: { $ifNull: ['$enquiryLogs', []] },
                    as: 'log',
                    cond: {
                      $eq: ['$$log.event', 'School tour completed'],
                    },
                  },
                },
                kitSellingLogs: {
                  $filter: {
                    input: { $ifNull: ['$enquiryLogs', []] },
                    as: 'log',
                    cond: {
                      $eq: ['$$log.event', 'Registration fee received'],
                    },
                  },
                },
              },
              in: {
                $cond: [
                  { $gt: [{ $size: '$$kitSellingLogs' }, 0] },
                  {
                    $dateToString: {
                      format: '%d-%m-%Y',
                      date: {
                        $arrayElemAt: ['$$kitSellingLogs.created_at', 0],
                      },
                    },
                  },
                  {
                    $cond: [
                      { $gt: [{ $size: '$$schoolTourLogs' }, 0] },
                      {
                        $dateToString: {
                          format: '%d-%m-%Y',
                          date: {
                            $arrayElemAt: ['$$schoolTourLogs.created_at', 0],
                          },
                        },
                      },
                      'NA',
                    ],
                  },
                ],
              },
            },
          },
          kit_sold_date: {
            $cond: {
              if: {
                $ifNull: ['$kit_sold_date', false],
              },
              then: {
                $dateToString: {
                  format: '%d-%m-%Y',
                  date: {
                    $convert: {
                      input: '$kit_sold_date',
                      to: 'date',
                      onError: null,
                      onNull: null,
                    },
                  },
                },
              },
              else: {
                $let: {
                  vars: {
                    registrationFeeReceivedLog: {
                      $filter: {
                        input: '$enquiryLogs',
                        as: 'log',
                        cond: {
                          $eq: [
                            '$$log.event',
                            EEnquiryEvent.REGISTRATION_FEE_RECEIVED,
                          ],
                        },
                      },
                    },
                  },
                  in: {
                    $cond: {
                      if: {
                        $ifNull: ['$$registrationFeeReceivedLog', false],
                      },
                      then: {
                        $dateToString: {
                          format: '%d-%m-%Y',
                          date: {
                            $arrayElemAt: [
                              '$$registrationFeeReceivedLog.created_at',
                              0,
                            ],
                          },
                        },
                      },
                      else: null,
                    },
                  },
                },
              },
            },
          },
          dropped_date: {
            $cond: {
              if: { $ifNull: ['$drop_date', false] },
              then: {
                $dateToString: {
                  format: '%d-%m-%Y',
                  date: {
                    $convert: {
                      input: '$drop_date',
                      to: 'date',
                      onError: null,
                      onNull: null,
                    },
                  },
                },
              },
              else: {
                $let: {
                  vars: {
                    enquiryClosedLog: {
                      $filter: {
                        input: '$enquiryLogs',
                        as: 'log',
                        cond: {
                          $eq: ['$$log.event', EEnquiryEvent.ENQUIRY_CLOSED],
                        },
                      },
                    },
                  },
                  in: {
                    $cond: {
                      if: { $gt: [{ $size: '$$enquiryClosedLog' }, 0] },
                      then: {
                        $dateToString: {
                          format: '%d-%m-%Y',
                          date: {
                            $convert: {
                              input: {
                                $arrayElemAt: [
                                  '$$enquiryClosedLog.created_at',
                                  0,
                                ],
                              },
                              to: 'date',
                              onError: null,
                              onNull: null,
                            },
                          },
                        },
                      },
                      else: null,
                    },
                  },
                },
              },
            },
          },
        },
      },
    ];

    const enquiryDetails = await this.enquiryRepository.aggregate(pipeline);
    if (!enquiryDetails.length) {
      throw new HttpException(
        'Enquiries not found for the provided academic year Id',
        HttpStatus.NOT_FOUND,
      );
    }

    const schoolIds = [];
    const enquiries = enquiryDetails.map((e: any) => {
      if (
        !schoolIds.includes(e.school_id) &&
        ![false, null, undefined, ''].includes(e.school_id)
      )
        schoolIds.push(e.school_id);
      let followUpdate = e?.next_follow_up;
      if (!e?.next_follow_up && e?.next_follow_up_date) {
        if (typeof e?.next_follow_up_date === 'string') {
          followUpdate =
            e?.next_follow_up_date.split('T')?.length > 0
              ? e?.next_follow_up_date
                  .split('T')[0]
                  .split('-')
                  .reverse()
                  .join('-')
              : null;
        } else if (typeof e?.next_follow_up_date === 'object') {
          followUpdate =
            e?.next_follow_up_date.toISOString().split('T')?.length > 0
              ? e?.next_follow_up_date
                  .toISOString()
                  .split('T')[0]
                  .split('-')
                  .reverse()
                  .join('-')
              : null;
        }
      }
      return {
        'School Id': e?.school_id ?? 'NA',
        'Enquiry No': e?.enquiry_number ?? 'NA',
        'Lead Generation Date': e.enquiry_date
          ? moment(e.enquiry_date).format('DD-MM-YYYY')
          : 'NA',
        'Student First Name': e?.student_first_name ?? 'NA',
        'Student Last Name': e?.student_last_name ?? 'NA',
        Board: e?.board ?? 'NA',
        Grade: e?.grade ?? 'NA',
        Division: e?.division ?? 'NA',
        Course: e?.course ?? 'NA',
        Stream: e?.stream ?? 'NA',
        Shift: e?.shift ?? 'NA',
        'Mandatory Profile % Updated': 'NA',
        'Profile % Updated': 'NA',
        'Enquiry Initiator': e?.created_by ?? 'NA',
        'Current Owner': e?.current_owner ?? 'NA',
        'Enquiry For': e?.enquiry_type ?? 'NA',
        'Enquiry for Academic Year': e?.academic_year ?? 'NA',
        'Is sibling': e?.sibling_details?.length ? 'YES' : 'NO',
        'Enquiry Status': e?.enquiry_status ?? 'NA',
        // 'Current Stage': e?.current_stage ?? 'NA',
        'Current Stage':
          this.enquiryHelper.getCurrentEnquiryStage(e?.enquiry_stages) ?? 'NA',
        'Mode of Contact': e?.enquiry_mode ?? 'NA',
        'Source Type': e?.enquiry_source_type ?? 'NA',
        'Enquiry Source': e?.enquiry_source ?? 'NA',
        'Enquiry Sub-Source': e?.enquiry_sub_source ?? 'NA',
        'Enquirer SSO Username': e?.enquirer_sso_details?.username ?? 'NA',
        'Enquirer SSO Password': e?.enquirer_sso_details?.password ?? 'NA',
        'Next Follow Up': followUpdate ?? 'NA',
        'Corporate Tie-ups': e?.corporate_tie_up ?? 'NA',
        'Pre school Tie-ups': e?.pre_school_tie_up ?? 'NA',
        'Parent Referral': e?.parent_referral ?? 'NA',
        'Employee Referral': e?.employee_referral ?? 'NA',
        Query: e?.query ?? 'NA',
        'Last Follow up Date': e?.last_follow_up_date ?? 'NA',
        'Last Follow up Remark': e?.last_follow_up_remark ?? 'NA',
        'School Tour Appointment Date': e?.school_tour_scheduled_date ?? 'NA',
        'Walk-In Date': e?.walkin_date ?? 'NA',
        'Admission Date': e?.admission_date ?? 'NA',
        'Kit Sold Date': e?.kist_sold_date ?? 'NA',
        'Dropped Date': e?.dropped_date ?? 'NA',
      };
    });

    const schoolDetails = await this.mdmService.postDataToAPI(
      MDM_API_URLS.SEARCH_SCHOOL,
      {
        operator: `school_id In (${schoolIds.toString()})`,
      },
    );

    const schoolDataIds = schoolDetails.data.schools.map(
      (school) => school.school_id,
    );

    const updatedRecords = [];
    if (schoolDetails?.data?.schools?.length) {
      updatedRecords.push(
        ...enquiries.map((enquiry) => {
          const index = schoolDataIds.indexOf(enquiry['School Id']);
          const schoolData = schoolDetails.data.schools[index];
          const updatedRecord = {
            'Business Vertical': schoolData?.lob_p2_description ?? 'NA',
            'Business Sub Vertical': schoolData?.lob_p1_description ?? 'NA',
            'Business Sub Sub Vertical': schoolData?.lob_description ?? 'NA',
            Cluster: schoolData?.cluster_name ?? 'NA',
            ...enquiry,
          };
          delete enquiry['School Id'];
          return updatedRecord;
        }),
      );
    } else {
      updatedRecords.push(
        ...enquiries.map((enquiry) => {
          const updatedRecord = {
            'Business Vertical': 'NA',
            'Business Sub Vertical': 'NA',
            'Business Sub Sub Vertical': 'NA',
            Cluster: 'NA',
            ...enquiry,
          };
          delete enquiry['School Id'];
          return updatedRecord;
        }),
      );
    }

    const fields = [
      'Business Vertical',
      'Business Sub Vertical',
      'Business Sub Sub Vertical',
      'Cluster',
      'Enquiry No',
      'Lead Generation Date',
      'Student First Name',
      'Student Last Name',
      'Board',
      'Grade',
      'Division',
      'Course',
      'Stream',
      'Shift',
      'Mandatory Profile % Updated',
      'Profile % Updated',
      'Enquiry Initiator',
      'Current Owner',
      'Enquiry For',
      'Enquiry for Academic Year',
      'Is sibling',
      'Enquiry Status',
      'Current Stage',
      'Mode of Contact',
      'Source Type',
      'Enquiry Source',
      'Enquiry Sub-Source',
      'Enquirer SSO Username',
      'Enquirer SSO Password',
      'Next Follow Up',
      'Corporate Tie-ups',
      'Pre school Tie-ups',
      'Parent Referral',
      'Employee Referral',
      'Query',
      'Last Follow up Date',
      'Last Follow up Remark',
      'School Tour Appointment Date',
      'Walk-In Date',
      'Admission Date',
      'Kit Sold Date',
      'Dropped Date',
    ];

    const date = new Date().toLocaleString('en-IN', {
      timeZone: 'Asia/Kolkata',
    });
    const [month, day, year] = date.split(',')[0].split('/');
    const filename = `Enquiry-Details-${day}-${month}-${year}-${date.split(',')[1].trimStart().split(' ')[0].split(':').join('')}`;

    const generatedCSV: any = await this.csvService.generateCsv(
      updatedRecords,
      fields,
      filename,
    );

    const file: Express.Multer.File =
      await this.fileService.createFileFromBuffer(
        Buffer.from(generatedCSV.csv),
        filename,
        'text/csv',
      );
    await this.setFileUploadStorage();
    const uploadedFileName = await this.storageService.uploadFile(
      file,
      filename,
    );
    const bucketName = this.configService.get<string>('BUCKET_NAME');

    if (!uploadedFileName) {
      throw new HttpException(
        'Something went wrong while uploading file!',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    const signedUrl = await this.storageService.getSignedUrl(
      bucketName,
      uploadedFileName,
      false,
    );
    return {
      url: signedUrl,
      fileName: uploadedFileName,
    };
  }

  // report export To Csv for admission Enquiry
  async admissionEnquiryReport(academicYearId: number) {
    const pipeline = [
      {
        $match: { 'academic_year.id': academicYearId },
      },
      {
        $lookup: {
          from: 'enquiryType',
          localField: 'enquiry_type_id',
          foreignField: '_id',
          as: 'enquiry_type_details',
          pipeline: [
            {
              $project: {
                name: 1,
              },
            },
          ],
        },
      },
      {
        $lookup: {
          from: 'admission',
          localField: '_id',
          foreignField: 'enquiry_id',
          as: 'admissionDetails',
        },
      },
      {
        $lookup: {
          from: 'followUps',
          localField: '_id',
          foreignField: 'enquiry_id',
          as: 'lastFollowUps',
          pipeline: [
            {
              $sort: {
                _id: -1,
              },
            },
          ],
        },
      },
      {
        $lookup: {
          from: 'enquiryLogs',
          localField: '_id',
          foreignField: 'enquiry_id',
          as: 'enquiryLogs',
          pipeline: [
            {
              $match: {
                event: {
                  $in: [
                    EEnquiryEvent.REGISTRATION_FEE_RECEIVED,
                    EEnquiryEvent.SCHOOL_TOUR_SCHEDULED,
                    EEnquiryEvent.SCHOOL_TOUR_RESCHEDULE,
                    EEnquiryEvent.SCHOOL_TOUR_COMPLETED,
                    EEnquiryEvent.COMPETENCY_TEST_SCHEDULED,
                    EEnquiryEvent.COMPETENCY_TEST_RESCHEDULED,
                    EEnquiryEvent.ADMISSION_APPROVED,
                    EEnquiryEvent.ENQUIRY_CLOSED,
                  ],
                },
              },
            },
            {
              $sort: {
                _id: -1,
              },
            },
          ],
        },
      },
      // {
      //   $addFields: {
      //     completedStages: {
      //       $filter: {
      //         input: '$enquiry_stages',
      //         as: 'stage',
      //         cond: {
      //           $or: [
      //             { $eq: ['$$stage.status', EEnquiryStageStatus.COMPLETED] },
      //             { $eq: ['$$stage.status', EEnquiryStageStatus.PASSED] },
      //           ],
      //         },
      //       },
      //     },
      //   },
      // },
      // {
      //   $addFields: {
      //     lastCompletedStage: {
      //       $arrayElemAt: [
      //         '$completedStages',
      //         { $subtract: [{ $size: '$completedStages' }, 1] },
      //       ],
      //     },
      //   },
      // },
      // {
      //   $addFields: {
      //     lastCompletedStageIndex: {
      //       $indexOfArray: [
      //         '$enquiry_stages.stage_name',
      //         '$lastCompletedStage.stage_name',
      //       ],
      //     },
      //   },
      // },
      {
        $addFields: {
          // nextStage: {
          //   $cond: {
          //     if: {
          //       $eq: ['$lastCompletedStageIndex', 7],
          //     },
          //     then: {
          //       $arrayElemAt: ['$enquiry_stages', '$lastCompletedStageIndex'],
          //     },
          //     else: {
          //       $arrayElemAt: [
          //         '$enquiry_stages',
          //         {
          //           $add: ['$lastCompletedStageIndex', 1],
          //         },
          //       ],
          //     },
          //   },
          // },
          nextFollowUpDate: {
            $cond: {
              if: {
                $and: [
                  { $eq: ['$enquiry_mode.value', 'Digital (website)'] },
                  { $eq: [{ $size: '$lastFollowUps' }, 0] },
                ],
              },
              then: '$created_at',
              else: {
                $cond: {
                  if: { $gt: [{ $size: '$lastFollowUps' }, 0] },
                  then: {
                    $dateFromString: {
                      dateString: {
                        $arrayElemAt: ['$lastFollowUps.date', 0],
                      },
                    },
                  },
                  else: null,
                },
              },
            },
          },
        },
      },
      {
        $addFields: {
          overdue_days_of_follow_up: {
            $cond: {
              if: {
                $ne: ['$nextFollowUpDate', null],
              },
              then: {
                $max: [
                  {
                    $dateDiff: {
                      startDate: '$nextFollowUpDate',
                      endDate: '$$NOW',
                      unit: 'day',
                    },
                  },
                  0,
                ],
              },
              else: null,
            },
          },
          document_status: {
            $cond: {
              if: {
                $gt: [
                  {
                    $size: {
                      $filter: {
                        input: '$documents',
                        as: 'doc',
                        cond: {
                          $and: [
                            { $eq: ['$$doc.file', null] },
                            { $eq: ['$$doc.is_mandatory', 1] },
                          ],
                        },
                      },
                    },
                  },
                  0,
                ],
              },
              then: 'Pending',
              else: 'Submitted',
            },
          },
        },
      },
      {
        $project: {
          _id: 1,
          school_name: '$school_location.value',
          school_id: '$school_location.id',
          enquiry_number: '$enquiry_number',
          enquiry_date: '$enquiry_date',
          student_first_name: '$student_details.first_name',
          student_last_name: '$student_details.last_name',
          enquiry_stages: 1,
          board: '$board.value',
          grade: '$student_details.grade.value',
          course: '$course.value',
          stream: '$stream.value',
          shift: '$shift.value',
          created_by: '$created_by.user_name',
          current_owner: '$assigned_to',
          enquiry_type: '$enquiry_type_details.name',
          enquiry_category: '$enquiry_category.value',
          academic_year: '$academic_year.value',
          sibling_details: '$sibling_details',
          enquiry_mode: '$enquiry_mode.value',
          enquiry_source_type: '$enquiry_source_type.value',
          enquiry_source: '$enquiry_source.value',
          enquiry_sub_source: '$enquiry_sub_source.value',
          school_location: '$school_location.value',
          parent_type: '$other_details.parent_type',
          created_at: '$created_at',
          school_tour_scheduled_date: {
            $cond: {
              if: {
                $gt: [{ $size: '$enquiryLogs' }, 0],
              },
              then: {
                $let: {
                  vars: {
                    scheduledSchoolTourLog: {
                      $filter: {
                        input: '$enquiryLogs',
                        as: 'log',
                        cond: {
                          $in: [
                            '$$log.event',
                            [
                              EEnquiryEvent.SCHOOL_TOUR_SCHEDULED,
                              EEnquiryEvent.SCHOOL_TOUR_RESCHEDULE,
                            ],
                          ],
                        },
                      },
                    },
                  },
                  in: {
                    $ifNull: [
                      {
                        $arrayElemAt: [
                          '$$scheduledSchoolTourLog.log_data.date',
                          0,
                        ],
                      },
                      null,
                    ],
                  },
                },
              },
              else: null,
            },
          },
          // Replace the empty walkin_date: {} in your $project stage with this logic:

          walkin_date: {
            $let: {
              vars: {
                schoolTourLogs: {
                  $filter: {
                    input: { $ifNull: ['$enquiryLogs', []] },
                    as: 'log',
                    cond: {
                      $eq: ['$$log.event', 'School tour completed'],
                    },
                  },
                },
                kitSellingLogs: {
                  $filter: {
                    input: { $ifNull: ['$enquiryLogs', []] },
                    as: 'log',
                    cond: {
                      $eq: ['$$log.event', 'Registration fee received'],
                    },
                  },
                },
              },
              in: {
                $cond: [
                  { $gt: [{ $size: '$$kitSellingLogs' }, 0] },
                  {
                    $dateToString: {
                      format: '%d-%m-%Y',
                      date: {
                        $arrayElemAt: ['$$kitSellingLogs.created_at', 0],
                      },
                    },
                  },
                  {
                    $cond: [
                      { $gt: [{ $size: '$$schoolTourLogs' }, 0] },
                      {
                        $dateToString: {
                          format: '%d-%m-%Y',
                          date: {
                            $arrayElemAt: ['$$schoolTourLogs.created_at', 0],
                          },
                        },
                      },
                      'NA',
                    ],
                  },
                ],
              },
            },
          },
          enquiry_status: {
            $cond: {
              if: {
                $eq: ['$status', EEnquiryStatus.ADMITTED],
              },
              then: EEnquiryStatus.CLOSED,
              else: '$status',
            },
          },
          // current_stage: {
          //   $cond: {
          //     if: {
          //       $eq: ['$lastCompletedStageIndex', 4],
          //     },
          //     then: {
          //       $cond: {
          //         if: {
          //           $eq: ['$nextStage.status', EEnquiryStageStatus.REJECTED],
          //         },
          //         then: 'Admission Cancelled',
          //         else: {
          //           $cond: {
          //             if: {
          //               $eq: [
          //                 '$nextStage.status',
          //                 EEnquiryStageStatus.APPROVED,
          //               ],
          //             },
          //             then: 'Admission Approved',
          //             else: '$nextStage.stage_name',
          //           },
          //         },
          //       },
          //     },
          //     else: '$nextStage.stage_name',
          //   },
          // },
          enrolment_number: {
            $cond: {
              if: {
                $gt: [{ $size: '$admissionDetails' }, 0],
              },
              then: {
                $let: {
                  vars: {
                    admissionRecordWithEnrolmentNumber: {
                      $filter: {
                        input: '$admissionDetails',
                        as: 'record',
                        cond: {
                          $ne: ['$$record.enrolment_number', null],
                        },
                      },
                    },
                  },
                  in: {
                    $cond: {
                      if: {
                        $gt: ['$$admissionRecordWithEnrolmentNumber', 0],
                      },
                      then: {
                        $arrayElemAt: [
                          '$$admissionRecordWithEnrolmentNumber.enrolment_number',
                          0,
                        ],
                      },
                      else: null,
                    },
                  },
                },
              },
              else: null,
            },
          },
          father_name: {
            $concat: [
              { $ifNull: ['$parent_details.father_details.first_name', ''] },
              ' ',
              { $ifNull: ['$parent_details.father_details.last_name', ''] },
            ],
          },
          mother_name: {
            $concat: [
              { $ifNull: ['$parent_details.mother_details.first_name', ''] },
              ' ',
              { $ifNull: ['$parent_details.mother_details.last_name', ''] },
            ],
          },
          father_mobile_number: '$parent_details.father_details.mobile',
          mother_mobile_number: '$parent_details.mother_details.mobile',
          father_email_id: '$parent_details.father_details.email',
          mother_email_id: '$parent_details.mother_details.email',
          enquirer_sso_details: {
            $switch: {
              branches: [
                {
                  case: {
                    $eq: ['$other_details.parent_type', EParentType.FATHER],
                  },
                  then: {
                    username: {
                      $ifNull: [
                        '$parent_details.father_details.sso_username',
                        null,
                      ],
                    },
                    password: {
                      $ifNull: [
                        '$parent_details.father_details.sso_password',
                        null,
                      ],
                    },
                  },
                },
                {
                  case: {
                    $eq: ['$other_details.parent_type', EParentType.MOTHER],
                  },
                  then: {
                    username: {
                      $ifNull: [
                        '$parent_details.mother_details.sso_username',
                        null,
                      ],
                    },
                    password: {
                      $ifNull: [
                        '$parent_details.mother_details.sso_password',
                        null,
                      ],
                    },
                  },
                },
                {
                  case: {
                    $eq: ['$other_details.parent_type', EParentType.GUARDIAN],
                  },
                  then: {
                    username: {
                      $ifNull: [
                        '$parent_details.guardian_details.sso_username',
                        null,
                      ],
                    },
                    password: {
                      $ifNull: [
                        '$parent_details.guardian_details.sso_password',
                        null,
                      ],
                    },
                  },
                },
              ],
              default: null,
            },
          },
          document_status: '$document_status',
          next_follow_up_at: '$next_follow_up_at',
          next_follow_up_date: {
            $cond: {
              if: {
                $ne: ['$nextFollowUpDate', null],
              },
              then: {
                $dateToString: {
                  format: '%d-%m-%Y',
                  date: '$nextFollowUpDate',
                },
              },
              else: null,
            },
          },
          next_follow_up_date_overdue_days: '$overdue_days_of_follow_up',
          admission_date: {
            $cond: {
              if: {
                $ifNull: ['$cancelled_admission_date', false],
              },
              then: {
                $dateToString: {
                  format: '%d-%m-%Y',
                  date: '$cancelled_admission_date',
                },
              },
              else: {
                $cond: {
                  if: {
                    $gt: [{ $size: '$admissionDetails' }, 0],
                  },
                  then: {
                    $dateToString: {
                      format: '%d-%m-%Y',
                      date: {
                        $arrayElemAt: ['$admissionDetails.admitted_at', 0],
                      },
                    },
                  },
                  else: null,
                },
              },
            },
          },
          date_of_registration: {
            $cond: {
              if: {
                $ne: ['$registered_at', null],
              },
              then: {
                $dateToString: {
                  format: '%d-%m-%Y',
                  date: '$registered_at',
                },
              },
              else: null,
            },
          },
          date_of_interaction: {
            $cond: {
              if: {
                $and: [
                  {
                    $gt: [{ $size: '$enquiryLogs' }, 0],
                  },
                  {
                    $in: [
                      '$student_details.grade.value',
                      ['Playschool', 'Nursery', 'Jr.KG', 'Sr.KG'],
                    ],
                  },
                ],
              },
              then: {
                $let: {
                  vars: {
                    competencyTestLog: {
                      $filter: {
                        input: '$enquiryLogs',
                        as: 'log',
                        cond: {
                          $in: [
                            '$$log.event',
                            [
                              EEnquiryEvent.COMPETENCY_TEST_SCHEDULED,
                              EEnquiryEvent.COMPETENCY_TEST_RESCHEDULED,
                            ],
                          ],
                        },
                      },
                    },
                  },
                  in: {
                    $arrayElemAt: ['$$competencyTestLog.log_data.date', 0],
                  },
                },
              },
              else: null,
            },
          },
          competency_test_date: {
            $cond: {
              if: {
                $and: [
                  {
                    $gt: [{ $size: '$enquiryLogs' }, 0],
                  },
                  {
                    $not: {
                      $in: [
                        '$student_details.grade.value',
                        ['Playschool', 'Nursery', 'Jr.KG', 'Sr.KG'],
                      ],
                    },
                  },
                ],
              },
              then: {
                $let: {
                  vars: {
                    competencyTestLog: {
                      $filter: {
                        input: '$enquiryLogs',
                        as: 'log',
                        cond: {
                          $in: [
                            '$$log.event',
                            [
                              EEnquiryEvent.COMPETENCY_TEST_SCHEDULED,
                              EEnquiryEvent.COMPETENCY_TEST_RESCHEDULED,
                            ],
                          ],
                        },
                      },
                    },
                  },
                  in: {
                    $arrayElemAt: ['$$competencyTestLog.log_data.date', 0],
                  },
                },
              },
              else: null,
            },
          },
          admission_approved_date: {
            $cond: {
              if: {
                $gt: [{ $size: '$enquiryLogs' }, 0],
              },
              then: {
                $let: {
                  vars: {
                    admissionApprovedLog: {
                      $filter: {
                        input: '$enquiryLogs',
                        as: 'log',
                        cond: {
                          $eq: [
                            '$$log.event',
                            EEnquiryEvent.ADMISSION_APPROVED,
                          ],
                        },
                      },
                    },
                  },
                  in: {
                    $dateToString: {
                      format: '%d-%m-%Y',
                      date: {
                        $arrayElemAt: ['$$admissionApprovedLog.created_at', 0],
                      },
                    },
                  },
                },
              },
              else: null,
            },
          },
          enquiry_closed_date: {
            $cond: {
              if: { $ifNull: ['$drop_date', false] },
              then: {
                $dateToString: {
                  format: '%d-%m-%Y',
                  date: {
                    $convert: {
                      input: '$drop_date',
                      to: 'date',
                      onError: null,
                      onNull: null,
                    },
                  },
                },
              },
              else: {
                $let: {
                  vars: {
                    enquiryClosedLog: {
                      $filter: {
                        input: '$enquiryLogs',
                        as: 'log',
                        cond: {
                          $eq: ['$$log.event', EEnquiryEvent.ENQUIRY_CLOSED],
                        },
                      },
                    },
                  },
                  in: {
                    $cond: {
                      if: {
                        $ifNull: ['$$enquiryClosedLog', false],
                      },
                      then: {
                        $dateToString: {
                          format: '%d-%m-%Y',
                          date: {
                            $arrayElemAt: ['$$enquiryClosedLog.created_at', 0],
                          },
                        },
                      },
                      else: null,
                    },
                  },
                },
              },
            },
          },
          enquiry_closed_remark: {
            $cond: {
              if: {
                $gt: [{ $size: '$enquiryLogs' }, 0],
              },
              then: {
                $let: {
                  vars: {
                    enquiryClosedLog: {
                      $filter: {
                        input: '$enquiryLogs',
                        as: 'log',
                        cond: {
                          $eq: ['$$log.event', EEnquiryEvent.ENQUIRY_CLOSED],
                        },
                      },
                    },
                  },
                  in: {
                    $arrayElemAt: ['$$enquiryClosedLog.log_data.message', 0],
                  },
                },
              },
              else: null,
            },
          },
          student_id: {
            $cond: {
              if: { $gt: [{ $size: '$admissionDetails' }, 0] },
              then: { $arrayElemAt: ['$admissionDetails.student_id', 0] },
              else: null,
            },
          },
          kit_sold_date: {
            $cond: {
              if: {
                $ifNull: ['$kit_sold_date', false],
              },
              then: {
                $dateToString: {
                  format: '%d-%m-%Y',
                  date: {
                    $convert: {
                      input: '$kit_sold_date',
                      to: 'date',
                      onError: null,
                      onNull: null,
                    },
                  },
                },
              },
              else: {
                $let: {
                  vars: {
                    registrationFeeReceivedLog: {
                      $filter: {
                        input: '$enquiryLogs',
                        as: 'log',
                        cond: {
                          $eq: ['$$log.event', 'Registration fee received'],
                        },
                      },
                    },
                  },
                  in: {
                    $cond: {
                      if: {
                        $ifNull: ['$$registrationFeeReceivedLog', false],
                      },
                      then: {
                        $dateToString: {
                          format: '%d-%m-%Y',
                          date: {
                            $arrayElemAt: [
                              '$$registrationFeeReceivedLog.created_at',
                              0,
                            ],
                          },
                        },
                      },
                      else: null,
                    },
                  },
                },
              },
            },
          },
          utm_source: '$other_details.utm_source',
          utm_medium: '$other_details.utm_medium',
          utm_campaign: '$other_details.utm_campaign',
          gcl_id: '$other_details.gcl_id',
        },
      },
      {
        $sort: { created_at: -1 },
      },
    ];
    console.log('admissionEnquiryReport-pipeline-', JSON.stringify(pipeline));

    const enquiryDetails = await this.enquiryRepository.aggregate(pipeline);

    if (!enquiryDetails.length) {
      throw new HttpException(
        'Enquiries not found for the provided academic year id',
        HttpStatus.NOT_FOUND,
      );
    }

    const schoolIds = [];
    const enquiries = enquiryDetails.map((e: any) => {
      if (
        !schoolIds.includes(e.school_id) &&
        ![false, null, undefined, ''].includes(e.school_id)
      )
        schoolIds.push(e.school_id);

      let followUpdate = e?.next_follow_up_date;
      let followUpdateOverdueDays = e?.next_follow_up_date_overdue_days;
      if (!e?.next_follow_up && e?.next_follow_up_at) {
        if (typeof e?.next_follow_up_at === 'string') {
          followUpdate =
            e?.next_follow_up_at.split('T')?.length > 0
              ? e?.next_follow_up_at
                  .split('T')[0]
                  .split('-')
                  .reverse()
                  .join('-')
              : null;
        } else if (typeof e?.next_follow_up_at === 'object') {
          followUpdate =
            e?.next_follow_up_at.toISOString().split('T')?.length > 0
              ? e?.next_follow_up_at
                  .toISOString()
                  .split('T')[0]
                  .split('-')
                  .reverse()
                  .join('-')
              : null;
        }
      }
      if (!e?.followUpdateOverdueDays && e?.next_follow_up_at) {
        const today = new Date();
        const inputDate =
          typeof e?.next_follow_up_at === 'string'
            ? new Date(e?.next_follow_up_at)
            : e?.next_follow_up_at;
        if (inputDate.getTime() < today.getTime()) {
          const diffInMs = today.getTime() - inputDate.getTime();
          const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));
          followUpdateOverdueDays = diffInDays;
        } else {
          followUpdateOverdueDays = 0;
        }
      }

      return {
        'School Id': e?.school_id ?? 'NA',
        'Enquiry No': e?.enquiry_number ?? 'NA',
        'Lead Generation Date': moment(e.enquiry_date).format('DD-MM-YYYY'),
        'Student First Name': e?.student_first_name ?? 'NA',
        'Student Last Name': e?.student_last_name ?? 'NA',
        Board: e?.board ?? 'NA',
        Grade: e?.grade ?? 'NA',
        // Division: e?.division ?? 'NA',
        Course: e?.course ?? 'NA',
        Stream: e?.stream ?? 'NA',
        Shift: e?.shift ?? 'NA',
        // 'Mandatory Profile % Updated': 'NA',
        // 'Profile % Updated': 'NA',
        'Enquiry Initiator': e?.created_by ?? 'NA',
        'Current Owner': e?.current_owner ?? 'NA',
        'Enquiry For': e?.enquiry_type[0] ?? 'NA',
        'Enquiry for Academic Year': e?.academic_year ?? 'NA',
        'Mode of Contact': e?.enquiry_mode ?? 'NA',
        'Source Type': e?.enquiry_source_type ?? 'NA',
        'Enquiry Source': e?.enquiry_source ?? 'NA',
        'Enquiry Sub-Source': e?.enquiry_sub_source ?? 'NA',
        'Enquiry Status': e?.enquiry_status ?? 'NA',
        // 'Current Stage': e?.current_stage ?? 'NA',
        'Current Stage':
          this.enquiryHelper.getCurrentEnquiryStage(e?.enquiry_stages) ?? 'NA',
        'Enrolment Number': e?.enrolment_number ?? 'NA',
        'Student Id': e?.student_id ?? 'NA',
        'Father Name': e?.father_name ?? 'NA',
        'Father Mobile No': e?.father_mobile_number ?? 'NA',
        'Father Email Id': e?.father_email_id ?? 'NA',
        'Mother Name': e?.mother_name ?? 'NA',
        'Mother Mobile No': e?.mother_mobile_number ?? 'NA',
        'Mother Email Id': e?.mother_email_id ?? 'NA',
        'Enquirer SSO Username': e?.enquirer_sso_details?.username ?? 'NA',
        'Enquirer SSO Password': e?.enquirer_sso_details?.password ?? 'NA',
        'Date of Walkin': e?.walkin_date ?? 'NA',
        'Date of Discovery': e?.school_tour_scheduled_date ?? 'NA',
        'Kit Sold Date': e?.kit_sold_date ?? 'NA',
        'Date of Registration': e?.date_of_registration ?? 'NA',
        'Date of Interaction': e?.date_of_interaction ?? 'NA',
        'Date of Test': e?.competency_test_date ?? 'NA',
        'Date of Admission Offered': e?.admission_approved_date ?? 'NA',
        'Date of Admission ': e?.admission_date ?? 'NA',
        'Dropped date': e?.enquiry_closed_date ?? 'NA',
        'Reason of dropped': e?.enquiry_closed_remark ?? 'NA',
        'Next Follow up action': 'NA',
        'Next Follow up date': followUpdate ?? 'NA',
        // 'Next Follow up overdue days':
        //   e?.next_follow_up_date_overdue_days ?? 'NA',
        'Next Follow up overdue days': followUpdateOverdueDays ?? 'NA',
        'Document Status': e?.document_status ?? 'NA',
        'UTM Source': e?.utm_source ?? 'NA',
        'UTM Medium': e?.utm_medium ?? 'NA',
        'UTM Campaign': e?.utm_campaign ?? 'NA',
        'GCL ID': e?.gcl_id ?? 'NA',
      };
    });

    const schoolDetails = await this.mdmService.postDataToAPI(
      MDM_API_URLS.SEARCH_SCHOOL,
      {
        operator: `school_id In (${schoolIds.toString()})`,
      },
    );

    const schoolDataIds = schoolDetails.data.schools.map(
      (school) => school.school_id,
    );

    const updatedRecords = [];
    if (schoolDetails?.data?.schools?.length) {
      updatedRecords.push(
        ...enquiries.map((enquiry) => {
          const index = schoolDataIds.indexOf(enquiry['School Id']);
          const schoolData = schoolDetails.data.schools[index];
          const updatedRecord = {
            'Business Vertical': schoolData?.lob_p2_description ?? 'NA',
            'Business Sub Vertical': schoolData?.lob_p1_description ?? 'NA',
            'Business Sub Sub Vertical': schoolData?.lob_description ?? 'NA',
            Cluster: schoolData?.cluster_name ?? 'NA',
            ...enquiry,
          };
          delete enquiry['School Id'];
          return updatedRecord;
        }),
      );
    } else {
      updatedRecords.push(
        ...enquiries.map((enquiry) => {
          const updatedRecord = {
            'Business Vertical': 'NA',
            'Business Sub Vertical': 'NA',
            'Business Sub Sub Vertical': 'NA',
            Cluster: 'NA',
            ...enquiry,
          };
          delete enquiry['School Id'];
          return updatedRecord;
        }),
      );
    }

    const fields = [
      'Business Vertical',
      'Business Sub Vertical',
      'Business Sub Sub Vertical',
      'Cluster',
      'Enquiry No',
      'Lead Generation Date',
      'Student First Name',
      'Student Last Name',
      'Board',
      'Grade',
      // 'Division',
      'Course',
      'Stream',
      'Shift',
      // 'Mandatory Profile % Updated',
      // 'Profile % Updated',
      'Enquiry Initiator',
      'Current Owner',
      'Enquiry For',
      'Enquiry for Academic Year',
      'Mode of Contact',
      'Source Type',
      'Enquiry Source',
      'Enquiry Sub-Source',
      'Enquiry Status',
      'Current Stage',
      'Enrolment Number',
      'Student Id',
      'Father Name',
      'Father Mobile No',
      'Father Email Id',
      'Mother Name',
      'Mother Mobile No',
      'Mother Email Id',
      'Enquirer SSO Username',
      'Enquirer SSO Password',
      'Date of Walkin',
      'Date of Discovery',
      'Date of Registration',
      'Kit Sold Date',
      'Date of Interaction',
      'Date of Test',
      'Date of Admission Offered',
      'Date of Admission ',
      'Dropped date',
      'Reason of dropped',
      'Next Follow up action',
      'Next Follow up date',
      'Next Follow up overdue days',
      'Document Status',
      'UTM Source',
      'UTM Medium',
      'UTM Campaign',
      'GCL ID',
    ];

    const date = new Date().toLocaleString('en-IN', {
      timeZone: 'Asia/Kolkata',
    });
    const [month, day, year] = date.split(',')[0].split('/');
    const filename = `Enquiry-to-Admission-${day}-${month}-${year}-${date.split(',')[1].trimStart().split(' ')[0].split(':').join('')}`;

    const generatedCSV: any = await this.csvService.generateCsv(
      updatedRecords,
      fields,
      filename,
    );

    const file: Express.Multer.File =
      await this.fileService.createFileFromBuffer(
        Buffer.from(generatedCSV.csv),
        filename,
        'text/csv',
      );
    await this.setFileUploadStorage();
    const uploadedFileName = await this.storageService.uploadFile(
      file,
      filename,
    );

    const bucketName = this.configService.get<string>('BUCKET_NAME');

    if (!uploadedFileName) {
      throw new HttpException(
        'Something went wrong while uploading file!',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    const signedUrl = await this.storageService.getSignedUrl(
      bucketName,
      uploadedFileName,
      false,
    );
    return {
      url: signedUrl,
      fileName: uploadedFileName,
    };
  }

  async triggerTermsAndConditionEmail(enquiryId: string): Promise<void> {
    const enquiryDetails = await this.enquiryRepository.getById(
      new Types.ObjectId(enquiryId),
    );
    await this.emailService.setEnquiryDetails(enquiryDetails).sendNotification(
      EMAIL_TEMPLATE_SLUGS.TERMS_AND_CONDITIONS,
      {
        action_url: FRONTEND_STANDALONE_PAGES_URL.TERMS_AND_CONDITIONS(
          enquiryId,
          enquiryDetails.school_location.id,
        ),
      },
      [
        this.enquiryHelper.getEnquirerDetails(enquiryDetails, 'email')
          ?.email as string,
      ],
    );
    await this.enquiryRepository.updateById(new Types.ObjectId(enquiryId), {
      'other_details.terms_and_conditions_email_sent': true,
    });
    return;
  }

  async addKitNumber(enquiryId: string, kitNumber: string): Promise<boolean> {
    const enquiryDetails = await this.enquiryRepository.getById(
      new Types.ObjectId(enquiryId),
    );
    if (!enquiryDetails) {
      throw new HttpException(
        'Enquiry details not found',
        HttpStatus.NOT_FOUND,
      );
    }
    const { kit_number } = enquiryDetails;

    if (!kit_number) {
      await this.enquiryRepository.updateById(new Types.ObjectId(enquiryId), {
        kit_number: kitNumber,
      });
      return true;
    }
    this.loggerService.log(`Kit number already exists`);
    return false;
  }

  async uploadFile(document: Express.Multer.File): Promise<{ path: string }> {
    await this.setFileUploadStorage();
    const uploadedFileName = await this.storageService.uploadFile(document);
    if (!uploadedFileName) {
      throw new HttpException(
        'Something went wrong while uploading file!',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
    return { path: uploadedFileName };
  }

  async getUploadedFileUrl({ path, isDownloadable }): Promise<{ url: string }> {
    const { bucketName } = await this.setFileUploadStorage();
    const uploadedFileUrl = await this.storageService.getSignedUrl(
      bucketName,
      path,
      isDownloadable,
    );
    if (!uploadedFileUrl) {
      throw new HttpException(
        'Something went wrong while getting file URL!',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
    return { url: uploadedFileUrl };
  }

  async handlePaymentDetails(paymentData: Record<string, any>, req: Request) {
    const { enquiry_id, payment_type } = paymentData;

    if (this.configService.get('NODE_ENV') === 'development') {
      await this.updatePaymentData(paymentData, req);
      return;
    }

    if (payment_type === EPaymentType.REGISTRATION) {
      await this.updatePaymentData(paymentData, req);
      return;
    } else if (
      payment_type === EPaymentType.ADMISSION ||
      payment_type === EPaymentType.CONSOLIDATED ||
      payment_type === EPaymentType.PSA ||
      payment_type === EPaymentType.KIDS_CLUB ||
      payment_type === EPaymentType.TRANSPORT
    ) {
      this.loggerService.log(
        `[AdmissionFee Queue][Publish][EnquiryId : ${enquiry_id}][Payload: ${JSON.stringify(paymentData)}]`,
      );
      await this.admissionFeeQueue.add('admissionFeePaymentDetails', {
        ...paymentData,
        url: req.url,
        headers: req.headers,
      });
      this.loggerService.log(
        `[AdmissionFee Queue][Publish][EnquiryId : ${enquiry_id}][Payload: ${JSON.stringify(paymentData)}][Data published]`,
      );
      return;
    }
  }

  async updateIvtEnquiryStatus(
    enquiryId: string,
    payload: UpdateIvtEnquiryStatusDto,
  ) {
    const enquiryDetails = await this.enquiryRepository.getById(
      new Types.ObjectId(enquiryId),
    );

    if (!enquiryDetails) {
      throw new HttpException(
        'Enquiry details not found',
        HttpStatus.NOT_FOUND,
      );
    }

    const { enquiry_stages, documents } = enquiryDetails;

    const admissionType = this.enquiryHelper.getAdmissionType(documents);
    enquiry_stages[enquiry_stages.length - 1].status = admissionType;

    await Promise.all([
      this.admissionRepository.updateByEnquiryId(
        new Types.ObjectId(enquiryId),
        {
          student_id: payload?.student_id,
          enrolment_number: payload?.enrolment_number,
          ...(payload?.gr_number ? { gr_number: payload?.gr_number } : {}),
        },
      ),
      this.enquiryRepository.updateById(new Types.ObjectId(enquiryId), {
        enquiry_stages: enquiry_stages,
      }),
    ]);

    return;
  }
}
