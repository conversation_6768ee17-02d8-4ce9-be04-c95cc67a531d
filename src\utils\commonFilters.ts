export const buildFilter = (column: string, operation: string, value: any) => {
  switch (operation) {
    case 'contains':
      return { [column]: { $regex: value, $options: 'i' } };
    case 'equals':
      return { [column]: value };
    case 'startsWith':
      return { [column]: { $regex: `^${value}`, $options: 'i' } };
    case 'endsWith':
      return { [column]: { $regex: `${value}$`, $options: 'i' } };
    case 'isEmpty':
      return { [column]: { $exists: true, $in: [null, '', 'N/A'] } };
    case 'isNotEmpty':
      return { [column]: { $exists: true, $nin: [null, '', 'N/A'] } };
    case 'isAnyOf':
      return { [column]: { $in: value } };
    case 'isWithin':
      return getDateRangeCondition(column, value);
    default:
      return {};
  }
};

const getDateRangeCondition = (column: string, values: string[]) => {
  const startDate = values[0].split('-').reverse().join('-');
  const endDate = values[1].split('-').reverse().join('-');

  switch (column) {
    case 'enquiryDate':
    case 'actionDate':
      return {
        ['created_at']: {
          $gte: new Date(`${startDate}T00:00:00.000Z`),
          $lte: new Date(`${endDate}T23:59:59.999Z`),
        },
      };
    case 'registrationDate':
      return {
        ['registered_at']: {
          $gte: new Date(`${startDate}T00:00:00.000Z`),
          $lte: new Date(`${endDate}T23:59:59.999Z`),
        },
      };
    case 'nextFollowUpDate':
      return {
        ['next_follow_up_at']: {
          $gte: new Date(`${startDate}T00:00:00.000Z`),
          $lte: new Date(`${endDate}T23:59:59.999Z`),
        },
      };
  }
};
