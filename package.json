{"name": "marketing", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "decrypt-env:dev": "rm -rf .env && NODE_ENV=development SECRET=EAlAiD3Pac4JeTwQcdHH node extract-env.js", "decrypt-env:stage": "rm -rf .env && NODE_ENV=staging SECRET=ou7sKLqBW52UX57L9GmH node extract-env.js", "decrypt-env:prod": "rm -rf .env && NODE_ENV=production SECRET=JCArWmeE1PwVueLdZyyv node extract-env.js", "decrypt-env:preprod": "rm -rf .env && NODE_ENV=preproduction SECRET=G3KiVTF1euELIIxqDkTy node extract-env.js", "encrypt-env:dev": "rm -rf .env.dev.enc && secure-env --secret EAlAiD3Pac4JeTwQcdHH && NODE_ENV=development node move-env.js", "encrypt-env:stage": "rm -rf .env.stg.enc && secure-env --secret ou7sKLqBW52UX57L9GmH && NODE_ENV=staging node move-env.js", "encrypt-env:prod": "rm -rf .env.prod.enc && secure-env --secret JCArWmeE1PwVueLdZyyv && NODE_ENV=production node move-env.js", "encrypt-env:preprod": "rm -rf .env.preprod.enc && secure-env --secret G3KiVTF1euELIIxqDkTy && NODE_ENV=preproduction node move-env.js", "prepare": "husky install"}, "dependencies": {"@nestjs/bullmq": "^11.0.2", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.2.0", "@nestjs/core": "^10.0.0", "@nestjs/mapped-types": "*", "@nestjs/mongoose": "^10.0.4", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^4.1.1", "@nestjs/swagger": "^7.3.1", "@nestjs/typeorm": "^10.0.2", "@types/lodash": "^4.17.0", "ampersand-common-module": "^1.0.27", "bullmq": "^5.46.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "date-fns-timezone": "^0.1.4", "dotenv": "^16.4.5", "express-session": "^1.18.0", "helmet": "^7.1.0", "ioredis": "^5.6.0", "moment": "^2.30.1", "moment-timezone": "^0.5.45", "mongoose": "8.8.0", "nest-keycloak-connect": "^1.10.0", "pdfkit": "^0.15.1", "pg": "^8.11.3", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "secure-env": "^1.2.0", "typeorm": "^0.3.20", "uuid": "^9.0.1", "winston": "^3.13.0", "zod": "^3.22.4"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.3.7", "@types/express": "^4.17.17", "@types/jest": "^29.5.12", "@types/node": "^20.3.1", "@types/pdfkit": "^0.13.6", "@types/supertest": "^6.0.0", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-simple-import-sort": "^12.0.0", "eslint-plugin-unused-imports": "^3.1.0", "husky": "^9.1.4", "jest": "^29.7.0", "lint-staged": "^15.2.7", "node-xlsx": "^0.24.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "lint-staged": {"*.ts": "eslint --cache --fix", "*.js": "eslint --cache --fix"}}