import { RequestMethod } from '@nestjs/common';

export const enquiryTypeAuthorizedRoutes = [
  {
    path: '/enquiry-type/metadata',
    method: RequestMethod.POST,
    permissions: '*',
    authenticate: true,
    authorize: true,
  },
  {
    path: '/enquiry-type/:enquiryTypeId/metadata',
    method: RequestMethod.PATCH,
    permissions: '*',
    authenticate: true,
    authorize: false,
  },
  {
    path: '/enquiry-type/:enquiryTypeId/map-stages',
    method: RequestMethod.PATCH,
    permissions: '*',
    authenticate: true,
    authorize: false,
  },
  {
    path: '/enquiry-type/list',
    method: RequestMethod.POST,
    permissions: '*',
    authenticate: true,
    authorize: false,
  },
  {
    path: '/enquiry-type/update/:enquiryTypeId',
    method: RequestMethod.PATCH,
    permissions: '*',
    authenticate: true,
    authorize: false,
  },
  {
    path: '/enquiry-type/delete/:enquiryTypeId',
    method: RequestMethod.DELETE,
    permissions: '*',
    authenticate: true,
    authorize: false,
  },
  {
    path: '/enquiry-type/changeStatus/:enquiryTypeId/:status',
    method: RequestMethod.POST,
    permissions: '*',
    authenticate: true,
    authorize: false,
  },
  {
    path: '/enquiry-type/checkSlugName',
    method: RequestMethod.POST,
    permissions: '*',
    authenticate: true,
    authorize: false,
  },
  {
    path: '/enquiry-type/active-list',
    method: RequestMethod.GET,
    permissions: '*',
    authenticate: true,
    authorize: false,
  },
  {
    path: '/enquiry-type/dropdown-list',
    method: RequestMethod.GET,
    permissions: '*',
    authenticate: true,
    authorize: false,
  },
  {
    path: '/enquiry-type/:enquiryTypeId',
    method: RequestMethod.GET,
    permissions: '*',
    authenticate: true,
    authorize: false,
  },
  {
    path: '/enquiry-type/:enquiryTypeId/formdata',
    method: RequestMethod.GET,
    permissions: '*',
    authenticate: true,
    authorize: false,
  },
  {
    path: '/enquiry-type/list/global-search',
    method: RequestMethod.GET,
    permissions: '*',
    authenticate: true,
    authorize: false,
  },
];
