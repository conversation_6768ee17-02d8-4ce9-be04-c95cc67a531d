import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  IsIn,
  <PERSON>N<PERSON>ber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';

export class UpdateAdmissionDto {
  @ApiProperty({ required: true })
  @IsString()
  workflow_id: string;

  @ApiProperty({ required: true })
  @IsIn([1, 2, 3], { message: 'The value must be either 1, 2, or 3' })
  status: number;

  @ApiProperty({ required: true })
  @IsString()
  comment: string;

  @ApiProperty({ required: false })
  workflow_details: any;
}

class DropdownValue {
  @ApiProperty()
  @IsNumber()
  id: number;

  @ApiProperty()
  @IsString()
  value: string;
}
export class CheckFeePayload {
  @ApiProperty()
  @IsString()
  enquiry_number: string;

  @ApiProperty({ type: DropdownValue })
  @ValidateNested()
  @Type(() => DropdownValue)
  school: DropdownValue;

  @ApiProperty({ type: DropdownValue })
  @ValidateNested()
  @Type(() => DropdownValue)
  brand: DropdownValue;

  @ApiProperty({ type: DropdownValue })
  @ValidateNested()
  @Type(() => DropdownValue)
  board: DropdownValue;

  @ApiProperty({ type: DropdownValue })
  @ValidateNested()
  @Type(() => DropdownValue)
  grade: DropdownValue;

  @ApiProperty({ type: DropdownValue })
  @ValidateNested()
  @Type(() => DropdownValue)
  course: DropdownValue;

  @ApiProperty({ type: DropdownValue })
  @ValidateNested()
  @Type(() => DropdownValue)
  shift: DropdownValue;

  @ApiProperty({ type: DropdownValue })
  @ValidateNested()
  @Type(() => DropdownValue)
  academicYearId: DropdownValue;

  @ApiProperty({ type: DropdownValue })
  @ValidateNested()
  @Type(() => DropdownValue)
  stream: DropdownValue;

  @ApiProperty({ type: [String] })
  @IsArray()
  @IsString({ each: true })
  studentFeeIds: string[];
}

export class UpDateAcStudentGuardianDto {
  @ApiProperty()
  @IsNumber()
  studentId: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  guardianId: number;

  @ApiProperty()
  @IsNumber()
  relationshipId: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  ParentfirstName: String;

  @ApiProperty()
  @IsString()
  @IsOptional()
  ParentMiddleName: String;

  @ApiProperty()
  @IsString()
  @IsOptional()
  ParentLastName: String;
  
  @ApiProperty()
  @IsString()
  @IsOptional()
  ParentMobile: String;
  
  @ApiProperty()
  @IsString()
  @IsOptional()
  ParentEmail: String;
}
export class UpdateAcStudentGuardianArrayDto {
  @ApiProperty({ type: [UpDateAcStudentGuardianDto] })
  @ValidateNested({ each: true })
  @Type(() => UpDateAcStudentGuardianDto)
  data: UpDateAcStudentGuardianDto[];
}

export class validEmailMobileCoGloble {
  @ApiProperty()
  @IsNumber()
  @IsOptional()
  email: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  mobile: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  enrolmentNumber: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  dob: number;
}