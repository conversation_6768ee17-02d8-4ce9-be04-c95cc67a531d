import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  IsIn,
  IsN<PERSON>ber,
  IsString,
  ValidateNested,
} from 'class-validator';

export class UpdateAdmissionDto {
  @ApiProperty({ required: true })
  @IsString()
  workflow_id: string;

  @ApiProperty({ required: true })
  @IsIn([1, 2, 3], { message: 'The value must be either 1, 2, or 3' })
  status: number;

  @ApiProperty({ required: true })
  @IsString()
  comment: string;

  @ApiProperty({ required: false })
  workflow_details: any;
}

class DropdownValue {
  @ApiProperty()
  @IsNumber()
  id: number;

  @ApiProperty()
  @IsString()
  value: string;
}
export class CheckFeePayload {
  @ApiProperty()
  @IsString()
  enquiry_number: string;

  @ApiProperty({ type: DropdownValue })
  @ValidateNested()
  @Type(() => DropdownValue)
  school: DropdownValue;

  @ApiProperty({ type: DropdownValue })
  @ValidateNested()
  @Type(() => DropdownValue)
  brand: DropdownValue;

  @ApiProperty({ type: DropdownValue })
  @ValidateNested()
  @Type(() => DropdownValue)
  board: DropdownValue;

  @ApiProperty({ type: DropdownValue })
  @ValidateNested()
  @Type(() => DropdownValue)
  grade: DropdownValue;

  @ApiProperty({ type: DropdownValue })
  @ValidateNested()
  @Type(() => DropdownValue)
  course: DropdownValue;

  @ApiProperty({ type: DropdownValue })
  @ValidateNested()
  @Type(() => DropdownValue)
  shift: DropdownValue;

  @ApiProperty({ type: DropdownValue })
  @ValidateNested()
  @Type(() => DropdownValue)
  academicYearId: DropdownValue;

  @ApiProperty({ type: DropdownValue })
  @ValidateNested()
  @Type(() => DropdownValue)
  stream: DropdownValue;

  @ApiProperty({ type: [String] })
  @IsArray()
  @IsString({ each: true })
  studentFeeIds: string[];
}
